import 'dart:io';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:markdown/markdown.dart' hide Text, Element;
import 'package:ulid/ulid.dart';

class MindmapCardPageController extends GetxController {
  // 已有数据
  final tabController = ShadTabsController(value: 'qa');
  List<Map<String, String>> get sourceList => [
    {"label": "Xmind", "value": "xmind"},
    {"label": "anki.mindmap.source_zhixi".tr, "value": "zhixi"},
    {"label": "anki.mindmap.source_mubu".tr, "value": "mubu"},
    {"label": "Markdown", "value": "markdown"},
    // {"label": "Logseq", "value": "logseq"},
  ];
  Map<String, List<Map<String, String>>> get maskTypesDict => {
    "xmind": [
      {"label": "anki.mubu.bold".tr, "value": "bold"},
      {"label": "anki.mubu.italic".tr, "value": "italic"},
      {"label": "anki.mubu.underline".tr, "value": "underline"},
      {"label": "anki.mindmap.strikeout".tr, "value": "strikeout"},
      {"label": "anki.mindmap.text_color".tr, "value": "text_color"},
    ],
    "zhixi": [
      {"label": "anki.mubu.bold".tr, "value": "bold"},
      {"label": "anki.mubu.italic".tr, "value": "italic"},
      {"label": "anki.mubu.underline".tr, "value": "underline"},
      {"label": "anki.mindmap.strikeout".tr, "value": "strikeout"},
      {"label": "anki.mindmap.text_color".tr, "value": "text_color"},
      {"label": "anki.mindmap.text_highlight_short".tr, "value": "text_highlight"},
    ],
    "markdown": [
      {"label": "anki.mubu.bold".tr, "value": "bold"},
      {"label": "anki.mubu.italic".tr, "value": "italic"},
      {"label": "anki.mubu.underline".tr, "value": "underline"},
      {"label": "anki.mindmap.strikeout".tr, "value": "strikeout"},
      {"label": "anki.mindmap.text_color".tr, "value": "text_color"},
      {"label": "anki.mindmap.text_highlight_short".tr, "value": "text_highlight"},
      {"label": "==xx==", "value": "==xx=="},
      {"label": "[[c1::xx]]", "value": "[[c1::xx]]"},
      {"label": "{{c1::xx}}", "value": "{{c1::xx}}"},
      {"label": "[[xx]]", "value": "[[xx]]"},
      {"label": "{{xx}}", "value": "{{xx}}"},
    ],
    "logseq": [
      {"label": "anki.mubu.bold".tr, "value": "bold"},
      {"label": "anki.mubu.italic".tr, "value": "italic"},
      {"label": "anki.mindmap.yellow_highlight".tr, "value": "highlight_yellow"},
      {"label": "anki.mindmap.red_highlight".tr, "value": "highlight_red"},
      {"label": "anki.mindmap.green_highlight".tr, "value": "highlight_green"},
      {"label": "anki.mindmap.blue_highlight".tr, "value": "highlight_blue"},
      {"label": "anki.mindmap.red_underline".tr, "value": "underline_red"},
      {"label": "anki.mindmap.green_underline".tr, "value": "underline_green"},
      {"label": "anki.mindmap.blue_underline".tr, "value": "underline_blue"},
      {"label": "anki.mindmap.red_font".tr, "value": "font_red"},
      {"label": "anki.mindmap.green_font".tr, "value": "font_green"},
      {"label": "anki.mindmap.blue_font".tr, "value": "font_blue"},
    ],
    "mubu": [
      {"label": "anki.mubu.bold".tr, "value": "bold"},
      {"label": "anki.mubu.italic".tr, "value": "italic"},
      {"label": "anki.mubu.underline".tr, "value": "underline"},
      {"label": "anki.mindmap.strikeout".tr, "value": "strikeout"},
      {"label": "anki.mindmap.text_color".tr, "value": "text_color"},
      {"label": "anki.mindmap.text_highlight_short".tr, "value": "text_highlight"},
    ],
  };
  final commonColorList = [
    {"label": "anki.mubu.red".tr, "value": "#ff0000"},
    {"label": "anki.mubu.green".tr, "value": "#00ff00"},
    {"label": "anki.mubu.blue".tr, "value": "#0000ff"},
    {"label": "anki.mubu.yellow".tr, "value": "#ffff00"},
  ].obs;
  final mubuColorList = [
    {"label": "anki.mubu.yellow".tr, "value": "yellow"},
    {"label": "anki.mubu.red".tr, "value": "red"},
    {"label": "anki.mubu.green".tr, "value": "green"},
    {"label": "anki.mubu.blue".tr, "value": "blue"},
    {"label": "anki.mubu.purple".tr, "value": "purple"},
  ].obs;
  final mubuHighlightColorList = [
    {"label": "anki.mubu.yellow".tr, "value": "yellow"},
    {"label": "anki.mubu.red".tr, "value": "red"},
    {"label": "anki.mubu.grey".tr, "value": "grey"},
    {"label": "anki.mubu.olive".tr, "value": "olive"},
    {"label": "anki.mubu.blue".tr, "value": "blue"},
    {"label": "anki.mubu.pink".tr, "value": "pink"},
    {"label": "anki.mubu.cyan".tr, "value": "cyan"},
  ].obs;
  final initalMaskTypes = {
    "xmind": ["bold"],
    "zhixi": ["bold"],
    "markdown": [
      "bold",
    ],
    "logseq": [
      "bold",
      "italic",
    ],
    "mubu": [
      "highlight",
      "bold",
    ],
  }.obs;
  // 表单参数
  final cardModel = "Kevin Mindmap Card v3".obs;
  final mindSource = 'xmind'.obs;
  final mapID = ''.obs;
  final parentDeck = ''.obs;
  final isCreateSubDeck = false.obs;
  final isClozeMode = false.obs;
  final maskTypes = <String>[].obs;
  final isObsidian = false.obs;
  final mediaFolder = "".obs;
  final primaryMaskColor = "ffff5656".obs;
  final secondaryMaskColor = "ffffeba2".obs;
  final textColorList = <String>[].obs;
  final highlightColorList = <String>[].obs;
  final isShowSource = true.obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final webviewController = Get.find<WebviewController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  @override
  void onInit() async {
    super.onInit();
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
  }



  Future<void> updateFieldList(String modelName) async {
    try {
      await AnkiConnectController().updateFieldList(modelName);
      update();
    } catch (e) {
      logger.e("updateFieldList error: $e");
    }
  }

  void submit() async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(null, "anki.mindmap.input_file_required".tr, "", type: "error");
      return;
    }
    // 获取导出模式
    final exportCardMode = settingController.getExportCardMode();
    final ankiConnectUrl = settingController.ankiConnectUrl.value;

    // 显示进度对话框
    progressController.reset(
      showOutputHint: exportCardMode == "apkg",
      numberButtons: exportCardMode == "apkg" ? 2 : 0,
    );
    progressController.showProgressDialog(Get.context!);
    List<String> imagePaths = [];
    for (var j = 0; j < selectedFilePaths.length; j++) {
      String path = selectedFilePaths[j];
      final finalMaskTypes = isClozeMode.value ? maskTypes.toList() : [];
      if (mindSource.value == "markdown") {
        String fileContent = await File(path).readAsString();
        if (isObsidian.value) {
          fileContent =
              AnkiConnectController().convertObsidianLinksEnhanced(fileContent);
        }
        String html = markdownToHtml(
          fileContent,
          blockSyntaxes: const [
            FencedCodeBlockSyntax(),
            TableSyntax(),
          ],
          inlineSyntaxes: [
            InlineHtmlSyntax(),
            EmphasisSyntax.asterisk(),
            ImageSyntax(),
            StrikethroughSyntax(),
            EmojiSyntax(),
            ColorSwatchSyntax(),
            AutolinkExtensionSyntax(),
          ],
          extensionSet: ExtensionSet.commonMark,
        );
        if (isClozeMode.value) {
          html = await webviewController.convertClozeForMarkdownMindmap(
              html, maskTypes, textColorList, highlightColorList);
        }
        // logger.i("html: $html");
        // 替换图片路径
        String mediaDir = PathUtils(path).parent;
        if (mediaFolder.value.isNotEmpty) {
          mediaDir = mediaFolder.value;
        }
        final (processedHtml, imgs) =
            await replaceHtmlBase64Img(html, mediaDir);
        imagePaths.addAll(imgs);
        final uid = Ulid().toString();
        final node = await webviewController.convertMD2JSON(processedHtml);
        final nodePath = PathUtils.join([await PathUtils.tempDir, "$uid.json"]);
        await File(nodePath).writeAsString(node);
        path = nodePath;
        logger.i("nodePath: $nodePath");
      }
      final data = {
        "mind_source": mindSource.value,
        "file_path": path,
        "parent_deck": parentDeck.value,
        "mask_types": finalMaskTypes,
        "img_paths": imagePaths,
        "text_colors": textColorList.toList(),
        "highlight_colors": highlightColorList.toList(),
        "tags": tags.toList(),
        "is_show_source": isShowSource.value,
        "output_path": await PathUtils.getOutputApkgPath(),
        "map_id": mapID.value,
        "address": ankiConnectUrl,
      };
      logger.d("data: $data");
      try {
        // 发送请求
        final resp = await messageController.request(data, "mindmap_card");
        logger.d("resp: $resp");
        if (resp.status == "success") {
          if (exportCardMode == "ankiconnect") {
            await AnkiConnectController().importApkg(resp.data, isDelete: true);
          }
          progressController.outputPath.value = resp.data;
          progressController
              .updateProgress(status: "completed", message: 'common.completed'.tr);
        }else{
          progressController.updateProgress(status: "error", message: resp.message);
          break;
        }
      } catch (e) {
        progressController.updateProgress(status: "error", message: e.toString());
        break;
      }
    }
  }
}
