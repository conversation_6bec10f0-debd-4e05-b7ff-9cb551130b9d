# This file was generated by Nuitka and describes the types of the
# created shared library.

# At this time it lists only the imports made and can be used by the
# tools that bundle libraries, including Nuitka itself. For instance
# standalone mode usage of the created library will need it.

# In the future, this will also contain type information for values
# in the module, so IDEs will use this. Therefore please include it
# when you make software releases of the extension module that it
# describes.

import os
import fitz
import json
import typing
import loguru
import loguru.logger
import posixpath
import pathlib
import re
import binascii
import glob
import requests
import traceback
import pdf2docx
import pdf2docx.Converter
import pandas
import argparse
import openai
import PIL
import PIL.Image
import io
import base64
import csv
import datetime
import utils
import cv2
import numpy
import easyocr
import scipy
import scipy.signal
import matplotlib
import matplotlib.pyplot
import unicodedata
import string
import math
import paddleocr
import time
import sys
import pdfrw
import pdfrw.PdfDict
import pdfrw.PdfReader
import pdfrw.PdfWriter
import codecs
import pdfrw.objects
import pdfrw.objects.PdfString
import pdfrw.objects.PdfName
import defusedxml
import defusedxml.ElementTree
import xml
import xml.etree
import xml.etree.ElementTree
import pdfrw.PdfTokens
import pdfrw.PdfArray
import pdfrw.PdfObject
import pdfrw.PdfString
import pdfrw.uncompress
import pdfrw.objects.pdfname
import functools
import asyncio
import edge_tts
import colorsys
import _frozen_importlib_external
import contextlib
import fastapi
import fastapi.FastAPI
import fastapi.Request
import fastapi.HTTPException
import fastapi.Response
import fastapi.responses
import pymongo
import pydantic
import pydantic.main
import pydantic.fields
import pydantic.generics
import uvicorn
import httpx
import jsonlines
import collections
import tqdm
import tqdm.asyncio
import urllib
import urllib.parse
import pymongo.errors
import aiohttp
import aiolimiter
import copy
import tempfile
import ulid

# This is not Python source even if it looks so. Make it clear for
# now. This was decided by PEP 484 designers.
__name__ = ...

