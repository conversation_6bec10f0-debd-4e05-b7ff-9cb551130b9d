import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:dio/dio.dart';
import 'dart:convert';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';

class CardParams extends GetxController {
  // 表单参数
  final parentDeck = ''.obs;
  final isCreateSubDeck = false.obs;
  final modelName = "Kevin Reader Card v2".obs;
  final source = 'my'.obs;
  final noteTypes = <String>['line', 'note'].obs;
  final isUseSep = true.obs;
  final sep = '---'.obs;
  final isCloze = false.obs;
  final clozeType = "{{xx}}".obs;
  final tags = <String>[].obs;
}

class ExportParams extends GetxController {
  // 表单参数
  final source = 'my'.obs;
  final noteTypes = <String>['line', 'note'].obs;
  final hotSortMethod = 'hot'.obs;
  final mySortMethod = 'createTime'.obs;
  final sortAscending = true.obs;
  final exportFormat = 'markdown'.obs;
  final exportCategory = <String>['line', 'note'].obs;
  final outputDir = ''.obs;
}

class WeReaderAPI {
  final String baseUrl = 'https://weread.qq.com';
  late final Dio _dio;

  WeReaderAPI({required Map<String, String> headers}) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      headers: headers,
      validateStatus: (status) => status! < 500,
    ));

    // 添加拦截器用于日志和错误处理
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        logger.i('anki.wereader_card.api_request'
            .trParams({'uri': options.uri.toString()}));
        return handler.next(options);
      },
      onResponse: (response, handler) {
        if (response.statusCode != 200) {
          return handler.reject(
            DioException(
              requestOptions: response.requestOptions,
              response: response,
              message: 'anki.wereader_card.api_request_failed'
                  .trParams({'statusCode': response.statusCode.toString()}),
            ),
          );
        }
        return handler.next(response);
      },
      onError: (error, handler) {
        logger.i('anki.wereader_card.api_error'
            .trParams({'message': error.message ?? ''}));
        return handler.next(error);
      },
    ));
  }

  // 更新 headers (比如更新 cookie)
  void updateHeaders(Map<String, String> headers) {
    _dio.options.headers = headers;
  }

  // 尝试刷新Cookie
  Future<bool> refreshCookie() async {
    try {
      final response = await _dio.head(
        '',
        options: Options(
          followRedirects: false,
          validateStatus: (status) => true,
        ),
      );

      final respCookie = response.headers.map['set-cookie'];
      if (respCookie == null || respCookie.isEmpty) {
        logger.i('anki.wereader_card.cookie_refresh_failed'.tr);
        return false;
      } else {
        logger.i('anki.wereader_card.cookie_expired_refreshed'.tr);
        _dio.options.headers['Cookie'] = respCookie;
        return true;
      }
    } on DioException catch (e) {
      logger.i('anki.wereader_card.cookie_refresh_error'
          .trParams({'message': e.message ?? ''}));
      return false;
    }
  }

  // 获取用户的笔记本
  Future<Map<String, dynamic>> getUserNotebooks() async {
    try {
      final response = await _dio.get('/api/user/notebook');
      return response.data;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_notebook_failed'.trParams({
        'message': e.message ?? '',
        'data': e.response?.data?.toString() ?? '',
        'cookie': _dio.options.headers['Cookie']?.toString() ?? ''
      });
    }
  }

  // 获取书籍详情
  Future<Map<String, dynamic>> getBookInfo(String bookId) async {
    try {
      final response = await _dio.get(
        '/web/book/info',
        queryParameters: {'bookId': bookId},
      );
      return response.data;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_book_info_failed'
          .trParams({'message': e.message ?? ''});
    }
  }

  // 获取书籍目录
  Future<List<dynamic>> getBookChapters(String bookId) async {
    try {
      final response = await _dio.post(
        '/web/book/chapterInfos',
        data: jsonEncode({
          'bookIds': [bookId],
        }),
      );

      if (response.data is Map &&
          response.data.containsKey('data') &&
          response.data['data'] is List &&
          response.data['data'].isNotEmpty) {
        final data = response.data['data'][0]['updated'];
        List<dynamic> chapters = [];
        for (var chapter in data) {
          if (chapter.containsKey('anchors')) {
            chapters.add({
              'level': chapter['level'] ?? 1,
              'title': chapter['title'],
            });
            for (var anchor in chapter['anchors']) {
              chapters.add({
                'level': anchor['level'],
                'title': anchor['title'],
              });
            }
          } else if (chapter.containsKey('level')) {
            chapters.add({
              'level': chapter['level'] ?? 1,
              'title': chapter['title'],
            });
          } else {
            chapters.add({
              'level': 1,
              'title': chapter['title'],
            });
          }
        }
        return chapters;
      }
      throw 'anki.wereader_card.get_chapters_format_error'.tr;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_chapters_failed'
          .trParams({'message': e.message ?? ''});
    }
  }

  // 获取书籍个人想法
  Future<Map<String, dynamic>> getBookReviews(String bookId) async {
    try {
      final response = await _dio.get(
        '/web/review/list',
        queryParameters: {
          'bookId': bookId,
          'listType': 11,
          'mine': 1,
          'synckey': 0,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_reviews_failed'
          .trParams({'message': e.message ?? ''});
    }
  }

  // 获取书籍划线
  Future<Map<String, dynamic>> getBookHighlights(String bookId) async {
    try {
      final response = await _dio.get(
        '/web/book/bookmarklist',
        queryParameters: {'bookId': bookId},
      );
      return response.data;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_highlights_failed'
          .trParams({'message': e.message ?? ''});
    }
  }

  // 获取书籍的热门划线
  Future<Map<String, dynamic>> getBookHotHighlights(String bookId) async {
    try {
      // 首先发送请求获取热门划线总数
      logger.i('anki.wereader_card.getting_hot_count'.tr);
      final countResponse = await _dio.get(
        '/web/book/bestbookmarks',
        queryParameters: {'bookId': bookId, 'count': 10},
      );

      // 检查返回数据
      if (countResponse.data == null ||
          !countResponse.data.containsKey('bestBookMarks')) {
        logger.e('anki.wereader_card.hot_highlights_format_error'.tr +
            ' ${countResponse.data}');
        throw 'anki.wereader_card.hot_highlights_format_error'.tr;
      }

      // 提取总划线数
      final totalCount = countResponse.data['bestBookMarks']['totalCount'] ?? 0;
      logger.i('热门划线总数: $totalCount');

      if (totalCount <= 0) {
        logger.i('anki.wereader_card.no_hot_highlights'.tr);
        return countResponse.data['bestBookMarks'];
      }

      // 如果总数大于10，再次请求获取所有热门划线
      logger.i('anki.wereader_card.getting_all_hot'
          .trParams({'count': totalCount.toString()}));
      final response = await _dio.get(
        '/web/book/bestbookmarks',
        queryParameters: {'bookId': bookId, 'count': totalCount},
      );

      // 检查数据是否有效
      if (response.data == null ||
          !response.data.containsKey('bestBookMarks')) {
        logger.e('anki.wereader_card.get_all_hot_format_error'.tr +
            ' ${response.data}');
        throw 'anki.wereader_card.get_all_hot_format_error'.tr;
      }

      logger.i('anki.wereader_card.get_all_hot_success'.tr);
      return response.data['bestBookMarks'];
    } on DioException catch (e) {
      logger.e('anki.wereader_card.get_hot_failed'
          .trParams({'message': e.message ?? ''}));
      throw 'anki.wereader_card.get_hot_failed'
          .trParams({'message': e.message ?? ''});
    } catch (e) {
      logger.e('anki.wereader_card.get_hot_unknown_error'
          .trParams({'error': e.toString()}));
      throw 'anki.wereader_card.get_hot_unknown_error'
          .trParams({'error': e.toString()});
    }
  }

  // 获取书籍阅读进度信息
  Future<Map<String, dynamic>> getBookProgress(String bookId) async {
    try {
      final response = await _dio.get(
        '/web/book/getProgress',
        queryParameters: {'bookId': bookId},
      );
      return response.data;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_progress_failed'
          .trParams({'message': e.message ?? ''});
    }
  }

  // 获取书架上所有书
  Future<List<Map<String, dynamic>>> getBookshelf() async {
    try {
      // 从 cookie 中获取 wr_vid
      final wrVid =
          _extractVidFromCookie(_dio.options.headers['Cookie'] as String);
      if (wrVid == null) {
        throw 'anki.wereader_card.cannot_get_vid'.tr;
      }

      final response = await _dio.get(
        '/web/shelf/sync',
        queryParameters: {'userVid': wrVid},
      );

      if (response.statusCode != 200) {
        throw 'anki.wereader_card.get_bookshelf_status_failed'
            .trParams({'statusCode': response.statusCode.toString()});
      }
      final data = response.data;
      final List<dynamic> rawBooks = data['books'] ?? [];
      final Set<Map<String, dynamic>> books = {};
      for (var book in rawBooks) {
        final String bookId = book['bookId']?.toString() ?? '';
        if (bookId.isEmpty) {
          continue;
        }
        // 确保必要字段存在
        if (book['title'] != null && book['author'] != null) {
          books.add({
            'bookId': bookId,
            'title': book['title'],
            'author': book['author'],
            'cover': book['cover'] ?? '',
          });
        }
      }

      // 转换为列表并按标题排序
      final sortedBooks = books.toList()
        ..sort(
            (a, b) => (a['title'] as String).compareTo(b['title'] as String));

      return sortedBooks;
    } on DioException catch (e) {
      throw 'anki.wereader_card.get_bookshelf_failed'
          .trParams({'message': e.message ?? ''});
    }
  }

  // 从 cookie 字符串中提取 wr_vid
  String? _extractVidFromCookie(String cookieStr) {
    final cookies = cookieStr
        .split(';')
        .map((c) => c.trim().split('='))
        .where((c) => c.length == 2)
        .map((c) => MapEntry(c[0], c[1]))
        .toList();

    for (var cookie in cookies) {
      if (cookie.key == 'wr_vid') {
        return cookie.value;
      }
    }
    return null;
  }
}

class WeReaderCardPageController extends GetxController {
  // 已有数据
  final tabController = ShadTabsController(value: 'card');
  final bookList = <Map<String, dynamic>>[].obs;
  final headers = {
    "User-Agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Host": "weread.qq.com",
    "Accept":
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Content-Type": "application/json",
    "Upgrade-Insecure-Requests": "1",
  }.obs;
  final sourceList = [
    {"label": "anki.wereader_card.my_notes".tr, "value": "my"},
    {"label": "anki.wereader_card.hot_highlights".tr, "value": "hot"},
  ].obs;
  final noteTypeList = [
    {"label": "anki.wereader_card.highlight_notes".tr, "value": "line"},
    {"label": "anki.wereader_card.review_notes".tr, "value": "note"},
  ].obs;
  final clozeGrammarList = [
    {"label": "{{xx}}", "value": "{{xx}}"},
    {"label": "[[xx]]", "value": "[[xx]]"},
    {"label": "((xx))", "value": "((xx))"},
  ].obs;
  final sepList = [
    {"label": "---", "value": "---"},
  ].obs;
  final exportCategoryList = [
    {"label": "anki.wereader_card.highlight_notes".tr, "value": "line"},
    {"label": "anki.wereader_card.review_notes".tr, "value": "note"},
  ].obs;
  final exportFormatList = [
    {"label": "Markdown", "value": "markdown"},
    // {"label": "微信默认", "value": "wechat"},
    // {"label": "纯文本", "value": "text"},
    {"label": "xlsx", "value": "xlsx"},
    {"label": "csv", "value": "csv"},
    {"label": "json", "value": "json"},
  ].obs;
  final hotSortMethodList = [
    {"label": "anki.wereader_card.sort_by_popularity".tr, "value": "hot"},
    {"label": "anki.wereader_card.sort_by_chapter".tr, "value": "chapter"},
  ].obs;
  final mySortMethodList = [
    {
      "label": "anki.wereader_card.sort_by_create_time".tr,
      "value": "createTime"
    },
    {"label": "anki.wereader_card.sort_by_chapter".tr, "value": "chapter"},
  ].obs;
  final sortDirectionList = [
    {"label": "anki.wereader_card.ascending_order".tr, "value": "true"},
    {"label": "anki.wereader_card.descending_order".tr, "value": "false"},
  ].obs;
  // 表单参数
  final cardParams = Get.put(CardParams());
  final exportParams = Get.put(ExportParams());
  final cookie = ''.obs;
  final book = ''.obs;
  final isLimitRange = false.obs;
  final beginDate = ''.obs;
  final endDate = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final cookieController = TextEditingController();
  final ankiConnectController = Get.find<AnkiConnectController>();
  late WeReaderAPI weReaderAPI;
  final isLoading = false.obs;
  final _storage = StorageManager();

  @override
  void onInit() async {
    super.onInit();
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      cardParams.parentDeck.value = ankiConnectController.parentDeckList.first;
    }

    exportParams.outputDir.value = await PathUtils.downloadDir;
    weReaderAPI = WeReaderAPI(headers: headers);

    // 尝试从存储中加载保存的Cookie
    final savedCookie =
        _storage.read(StorageBox.default_, 'wereader_cookie', '');
    if (savedCookie.isNotEmpty) {
      logger.i('Found saved WeReader cookie, attempting to use it');
      // 使用保存的Cookie自动登录
      headers['Cookie'] = savedCookie;
      weReaderAPI.updateHeaders(headers);

      // 尝试验证cookie
      if (await fetchBooks()) {
        logger.i('Saved cookie validation successful');
        cookie.value = savedCookie;
        if (bookList.isNotEmpty) {
          book.value = bookList.first['value'];
        }
      } else {
        logger.w('Saved cookie validation failed, clearing cookie');
        clearSavedCookie();
      }
    }

    // 监听 cookie 变化
    ever(cookie, (String value) {
      if (value.isNotEmpty) {
        headers['Cookie'] = value;
        weReaderAPI.updateHeaders(headers);
        // 自动获取书籍列表
        // fetchBooks();
      }
    });
  }

  /// 清除保存的Cookie
  void clearSavedCookie() {
    try {
      _storage.remove(StorageBox.default_, 'wereader_cookie');
      logger.i('Cleared saved WeReader cookie');
    } catch (e) {
      logger.e('Error clearing saved WeReader cookie: $e');
    }
  }

  Future<String> validateCookie(String value) async {
    if (value.isEmpty) {
      return "anki.wereader_card.cookie_cannot_empty".tr;
    }
    try {
      headers['Cookie'] = value;
      logger.i(headers);
      weReaderAPI.updateHeaders(headers);
      if (!(await fetchBooks())) {
        return "anki.wereader_card.cookie_validation_failed".tr;
      }

      // 验证成功，保存cookie到存储
      _storage.write(StorageBox.default_, 'wereader_cookie', value);
      logger.i('WeReader cookie saved to storage successfully');
      cookie.value = value;
      book.value = bookList.first['value'];
      return "";
    } catch (e) {
      logger.e(e);
      return "anki.wereader_card.cookie_validation_error"
          .trParams({'error': e.toString()});
    }
  }

  // 获取书籍列表
  Future<bool> fetchBooks() async {
    try {
      isLoading.value = true;
      final books = await weReaderAPI.getBookshelf();
      // 优先添加笔记本中的书籍
      List<Map<String, String>> mergedBooks = [];
      for (var book in books) {
        final Map<String, String> typedBook = {
          'label': "《${book['title']}》 - (${book['author']})",
          'value': book['bookId'] as String,
        };
        mergedBooks.add(typedBook);
      }
      // 按书名排序
      mergedBooks.sort((a, b) => a['label']!.compareTo(b['label']!));
      // 更新状态
      bookList.value = mergedBooks;
      return true;
    } catch (e) {
      logger.e(e);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 清除会话数据
  void clearSession() {
    cookie.value = '';
    headers.remove('Cookie');
    if (weReaderAPI != null) {
      weReaderAPI.updateHeaders(headers);
    }
    bookList.clear();
    book.value = '';

    // 同时清除存储的Cookie
    clearSavedCookie();
  }

  @override
  void onClose() {
    cardParams.dispose();
    exportParams.dispose();
    super.onClose();
  }

  /// 根据章节信息构建牌组名称
  String _buildDeckName(
      Map<String, dynamic> chapter, List<dynamic> chaptersWithLevel) {
    try {
      // 安全地解析数字，如果解析失败则提供默认值
      int level = 1;
      int chapterIdx = 0;
      final title = chapter['title'].toString();

      try {
        level = int.parse(chapter['level'].toString());
      } catch (e) {
        logger
            .w("Invalid level format for chapter $title: ${chapter['level']}");
      }

      try {
        chapterIdx = int.parse(chapter['chapterIdx'].toString());
      } catch (e) {
        logger.w(
            "Invalid chapterIdx format for chapter $title: ${chapter['chapterIdx']}");
      }

      // 如果是一级章节,直接拼接
      if (level == 1) {
        return "${cardParams.parentDeck.value}::$title";
      }

      // 构建完整的章节路径
      List<String> pathParts = [cardParams.parentDeck.value];

      try {
        // 根据 chapterIdx 定位当前章节在数组中的位置 (chapterIdx从1开始计数)
        int currentPos = chapterIdx - 1;
        if (currentPos >= 0 && currentPos < chaptersWithLevel.length) {
          // 从当前位置往前查找，直到找到 level 1 的章节
          List<String> chapterTitles = [];
          int currentLevel = level;

          // 从当前位置往前遍历
          for (int i = currentPos - 1; i >= 0; i--) {
            var parentChapter = chaptersWithLevel[i];
            int parentLevel = 1;
            try {
              parentLevel = int.parse(parentChapter['level'].toString());
            } catch (e) {
              continue;
            }

            // 如果找到更高层级的章节，记录下来
            if (parentLevel < currentLevel) {
              chapterTitles.insert(0, parentChapter['title']);
              currentLevel = parentLevel;

              // 如果已经找到一级章节，就可以停止了
              if (parentLevel == 1) {
                break;
              }
            }
          }

          // 添加所有找到的父章节标题
          pathParts.addAll(chapterTitles);
        }
      } catch (e) {
        logger.w("Error finding parent chapters: $e");
      }

      // 添加当前章节
      pathParts.add(title);

      return pathParts.join("::");
    } catch (e) {
      logger.e("Error building deck name for chapter $chapter: $e");
      return cardParams.parentDeck.value;
    }
  }

  Future<List<Map<String, dynamic>>> extractHotHighlights(
      String sortMethod, bool isAscending) async {
    try {
      final chaptersWithLevel = await weReaderAPI.getBookChapters(book.value);
      final result = await weReaderAPI.getBookHotHighlights(book.value);
      logger.i("result: $result");
      List<Map<String, dynamic>> highlights = [];
      final chapters = result['chapters'];

      // 检查API返回数据是否符合预期
      if (result['items'] == null) {
        logger.e("anki.wereader_card.hot_api_format_error".tr + ": $result");
        throw "anki.wereader_card.hot_api_format_error".tr;
      }

      // 建立章节索引映射
      Map<int, Map<String, dynamic>> chapterMap = {};
      for (var i = 0; i < chapters.length; i++) {
        final chapter = chapters[i];
        logger.i("chapter: $chapter");
        chapterMap[chapter['chapterUid']] = {
          'bookId': chapter['bookId'],
          'chapterIdx': chapter['chapterIdx'],
          'title': chaptersWithLevel[chapter['chapterIdx']]['title'],
          'level': chaptersWithLevel[chapter['chapterIdx']]['level']
        };
      }
      logger.i("chaptersWithLevel: ${chaptersWithLevel}");
      logger.i("chapters: ${chapters}");
      for (var highlight in result['items']) {
        try {
          final chapter = chapterMap[highlight['chapterUid']]!;
          highlights.add({
            'bookmarkId': highlight['bookmarkId'],
            'chapterUid': highlight['chapterUid'],
            'title': chapter['title'],
            'level': chapter['level'],
            'range': highlight['range'],
            'markText': highlight['markText'],
            'totalCount': highlight['totalCount'],
            'deckName': _buildDeckName(chapter, chaptersWithLevel),
          });
        } catch (e) {
          logger.e(e);
          highlights.add({
            'bookmarkId': highlight['bookmarkId'],
            'chapterUid': highlight['chapterUid'],
            'title': "",
            'level': "1",
            'range': highlight['range'],
            'markText': highlight['markText'],
            'totalCount': highlight['totalCount'],
            'deckName': cardParams.parentDeck.value,
          });
        }
      }

      // 保持原有的排序逻辑
      if (sortMethod == 'hot') {
        highlights.sort((a, b) => int.parse(b['totalCount'].toString())
            .compareTo(int.parse(a['totalCount'].toString())));
      } else {
        highlights.sort((a, b) {
          int chapterCompare = (int.parse(a['chapterUid'].toString()))
              .compareTo(int.parse(b['chapterUid'].toString()));
          if (chapterCompare != 0) return chapterCompare;
          String rangeA = a['range'] as String;
          String rangeB = b['range'] as String;
          int startA = int.parse(rangeA.split('-')[0]);
          int startB = int.parse(rangeB.split('-')[0]);
          return startA.compareTo(startB);
        });
      }
      if (!isAscending) {
        highlights = highlights.reversed.toList();
      }
      return highlights;
    } catch (e) {
      logger.e("anki.wereader_card.extract_hot_failed"
          .trParams({'error': e.toString()}));
      throw "anki.wereader_card.extract_hot_failed"
          .trParams({'error': e.toString()});
    }
  }

  Future<List<Map<String, dynamic>>> extractMyNotes(
      bool isHighlight, bool isReview, String sortMethod, bool isAscending,
      {String beginDate = "", String endDate = ""}) async {
    try {
      List<Map<String, dynamic>> objs = [];
      Set<String> uniqueKeys = {};
      // 获取书籍目录
      final chaptersWithLevel = await weReaderAPI.getBookChapters(book.value);
      logger.i('Book chapters:');
      logger.i(chaptersWithLevel);

      if (isReview) {
        final result = await weReaderAPI.getBookReviews(book.value);
        List<dynamic> reviews = result['reviews'];
        if (reviews == null) {
          logger.e("anki.wereader_card.reviews_api_format_error"
              .trParams({'result': result.toString()}));
          if (isHighlight) {
            // 如果只是想法失败，但还要处理划线，就继续
            logger.i("anki.wereader_card.continue_processing_highlights".tr);
          } else {
            throw "anki.wereader_card.reviews_api_format_error_simple".tr;
          }
        } else {
          // 建立章节索引映射
          Map<int, Map<String, dynamic>> chapterMap = {};
          for (var review in reviews) {
            try {
              // 安全地获取 chapterIdx，如果不存在则跳过
              final chapterIdx = review['review']?['chapterIdx'];
              final chapterUid = review['review']?['chapterUid'];
              // logger.i("chapterUid: $chapterUid, chapterIdx: $chapterIdx");
              if (chapterIdx == null) continue;
              chapterMap[chapterUid] = {
                'chapterIdx': chapterIdx,
                'title': chaptersWithLevel[chapterIdx]['title'],
                'level': chaptersWithLevel[chapterIdx]['level'],
              };
            } catch (e) {
              logger.e("Error mapping chapter: $e");
              continue;
            }
          }
          logger.w("chapterMap: ${chapterMap}");
          for (var review in reviews) {
            final key =
                "${review['review']['chapterUid']}-${review['review']['range']}";
            if (uniqueKeys.contains(key)) {
              continue;
            }
            try {
              final chapter = chapterMap[review['review']['chapterUid']]!;
              objs.add({
                "type": "note",
                "id": review['reviewId'],
                "createTime": review['review']['createTime'],
                "chapterUid": review['review']['chapterUid'],
                "markText": review['review']['abstract'],
                "comment": review['review']['content'],
                "range": review['review']['range'],
                "title": chaptersWithLevel[review['review']['chapterIdx']]
                    ['title'],
                "level": chaptersWithLevel[review['review']['chapterIdx']]
                    ['level'],
                'deckName': _buildDeckName(chapter, chaptersWithLevel),
              });
            } catch (e) {
              logger.e(e);
              objs.add({
                "type": "note",
                "id": review['reviewId'],
                "createTime": review['review']['createTime'],
                "chapterUid": review['review']['chapterUid'],
                "markText": review['review']['abstract'],
                "comment": review['review']['content'],
                "range": review['review']['range'],
                "title": "",
                "level": "1",
                "deckName": cardParams.parentDeck.value,
              });
            }
            uniqueKeys.add(key);
          }
        }
      }
      if (isHighlight) {
        final result2 = await weReaderAPI.getBookHighlights(book.value);
        final chapters = result2['chapters'];
        if (chapters == null || result2['updated'] == null) {
          logger.e("anki.wereader_card.highlights_api_format_error"
              .trParams({'result': result2.toString()}));
          throw "anki.wereader_card.highlights_api_format_error_simple".tr;
        }

        // 建立章节索引映射,用于根据chapterUid快速查找对应章节信息
        Map<int, Map<String, dynamic>> chapterMap = {};
        for (var chapter in chapters) {
          chapterMap[chapter['chapterUid']] = {
            'bookId': chapter['bookId'],
            'chapterIdx': chapter['chapterIdx'],
            'title': chaptersWithLevel[chapter['chapterIdx']]['title'],
            'level': chaptersWithLevel[chapter['chapterIdx']]['level']
          };
        }
        logger.i("chapterMap: ${chapterMap}");
        List<dynamic> highlights = result2['updated'];
        for (var highlight in highlights) {
          final key = "${highlight['chapterUid']}-${highlight['range']}";
          if (uniqueKeys.contains(key)) {
            continue;
          }
          try {
            final chapter = chapterMap[highlight['chapterUid']]!;
            objs.add({
              "type": "line",
              "id": highlight['bookmarkId'],
              "createTime": highlight['createTime'],
              "chapterUid": highlight['chapterUid'],
              "markText": highlight['markText'],
              "comment": "",
              "range": highlight['range'],
              "title": chapter['title'],
              "level": chapter['level'],
              'deckName': _buildDeckName(chapter, chaptersWithLevel),
            });
          } catch (e) {
            logger.e(e);
            objs.add({
              "type": "line",
              "id": highlight['bookmarkId'],
              "createTime": highlight['createTime'],
              "chapterUid": highlight['chapterUid'],
              "markText": highlight['markText'],
              "comment": "",
              "range": highlight['range'],
              "title": "",
              "level": "1",
              'deckName': cardParams.parentDeck.value,
            });
          }
          uniqueKeys.add(key);
        }
      }
      if (sortMethod == 'createTime') {
        objs.sort((a, b) => a['createTime'].compareTo(b['createTime']));
      } else if (sortMethod == "chapter") {
        // 按章节排序
        objs.sort((a, b) {
          // 先按 chapterUid 排序
          int chapterCompare = (int.parse(a['chapterUid'].toString()))
              .compareTo(int.parse(b['chapterUid'].toString()));
          if (chapterCompare != 0) return chapterCompare;
          // 再按 range 的起始位置排序
          String rangeA = a['range'] as String;
          String rangeB = b['range'] as String;
          int startA = int.parse(rangeA.split('-')[0]);
          int startB = int.parse(rangeB.split('-')[0]);
          return startA.compareTo(startB);
        });
      }
      if (!isAscending) {
        objs = objs.reversed.toList();
      }
      if (beginDate.isNotEmpty) {
        objs = objs
            .where((obj) => obj['createTime'] * 1000 >= int.parse(beginDate))
            .toList();
      }
      if (endDate.isNotEmpty) {
        objs = objs
            .where((obj) => obj['createTime'] * 1000 <= int.parse(endDate))
            .toList();
      }
      return objs;
    } catch (e) {
      logger.e("anki.wereader_card.extract_notes_failed"
          .trParams({'error': e.toString()}));
      // 尝试刷新Cookie并重试
      if (await weReaderAPI.refreshCookie()) {
        logger.i("anki.wereader_card.cookie_refreshed_retry".tr);
        return extractMyNotes(isHighlight, isReview, sortMethod, isAscending,
            beginDate: beginDate, endDate: endDate);
      }
      throw "anki.wereader_card.extract_notes_failed"
          .trParams({'error': e.toString()});
    }
  }

  Future<void> submit(BuildContext context) async {
    try {
      logger.w(book.value);
      final currentBook = bookList.firstWhere(
        (b) => b['value'] == book.value,
        orElse: () => {},
      );
      String bookName =
          currentBook.isNotEmpty ? currentBook['label'] ?? "" : "";
      final exportMode = settingController.getExportCardMode();
      if (tabController.selected == "card") {
        progressController.reset(
          showOutputHint: exportMode == "apkg",
          numberButtons: exportMode == "apkg" ? 2 : 0,
        );
        progressController.showProgressDialog(context);

        if (cardParams.source.value == 'hot') {
          final highlights = await extractHotHighlights("chapter", true);
          // logger.i(highlights);
          progressController.updateProgress(
              status: "running",
              current: 10,
              total: 100,
              message: "anki.wereader_card.processing".tr);
          // 转换热门划线为 AnkiNote
          List<AnkiNote> notes = [];
          for (var highlight in highlights) {
            final parts = highlight['deckName'].split('::');
            final sourceLabel =
                "${bookName} ${parts.length > 1 ? parts.sublist(1).join('->') : parts.join('')}";
            String sourceLink =
                '<a href="weread://reading?bId=${book.value}&style=1">${sourceLabel}</a>';
            notes.add(AnkiNote(
              deckName: cardParams.isCreateSubDeck.value
                  ? highlight['deckName']
                  : cardParams.parentDeck.value,
              modelName: "Kevin Reader Card v2",
              fields: [
                highlight['markText'],
                sourceLink,
                "anki.wereader_card.people_highlighted"
                    .trParams({'count': highlight['totalCount'].toString()}),
                '',
                '',
                '',
              ],
              guid: highlight['bookmarkId'].toString(),
              tags: cardParams.tags.where((tag) => tag.isNotEmpty).toList(),
            ));
          }

          // 根据导出模式处理
          if (exportMode == "apkg") {
            final outputPath = await PathUtils.getOutputApkgPath();
            final resp =
                await AnkiConnectController().genApkg(notes, [], outputPath);
            if (resp.status == "success") {
              progressController.outputPath.value = resp.data;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
            } else {
              progressController.updateProgress(
                status: "error",
                message: resp.message,
                current: 0.0,
                total: 100.0,
              );
            }
          } else if (exportMode == "ankiconnect") {
            final outputPath = await PathUtils.getOutputApkgPath();
            final resp =
                await AnkiConnectController().genApkg(notes, [], outputPath);
            if (resp.status == "success") {
              await AnkiConnectController()
                  .importApkg(resp.data, isDelete: true);
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
            } else {
              progressController.updateProgress(
                status: "error",
                message: resp.message,
                current: 0.0,
                total: 100.0,
              );
            }
          }
        } else {
          final objs = await extractMyNotes(
              cardParams.noteTypes.contains("line"),
              cardParams.noteTypes.contains("note"),
              "chapter",
              true,
              beginDate: beginDate.value,
              endDate: endDate.value);
          logger.i(objs);
          progressController.updateProgress(
              status: "running",
              current: 10,
              total: 100,
              message: "anki.wereader_card.processing".tr);

          // 转换笔记为 AnkiNote
          List<AnkiNote> notes = [];
          for (var obj in objs) {
            // 获取笔记内容和创建时间
            String review = obj['comment'] ?? '';
            final createTime =
                DateTime.fromMillisecondsSinceEpoch(obj['createTime'] * 1000)
                    .toString()
                    .split('.')[0];

            // 提取标签
            final tagMatches =
                RegExp(r'#([^\s#]+)').allMatches(review).toList();
            final tags = [...cardParams.tags.where((tag) => tag.isNotEmpty)];
            tags.addAll(tagMatches.map((m) => m.group(1)!));
            review = review.replaceAll(RegExp(r'#([^\s#]+)'), '').trim();

            // 检查是否为反转卡片
            String header = '', note = '', reverse = '';
            if (RegExp(r'^[~～]$', multiLine: true).hasMatch(review)) {
              review =
                  review.replaceAll(RegExp(r'^[~～]$', multiLine: true), '');
              reverse = '1';
            }
            if (cardParams.isUseSep.value) {
              final parts = review
                  .split(RegExp('^${cardParams.sep.value}\$', multiLine: true));
              if (parts.length > 1) {
                header = parts[0].trim().replaceAll('\n', '<br>');
                note =
                    parts.sublist(1).join('\n').trim().replaceAll('\n', '<br>');
              } else {
                if (reverse.isNotEmpty) {
                  header = review.trim().replaceAll('\n', '<br>');
                } else {
                  note = review.trim().replaceAll('\n', '<br>');
                }
              }
            } else {
              if (reverse.isNotEmpty) {
                header = review.trim().replaceAll('\n', '<br>');
              } else {
                note = review.trim().replaceAll('\n', '<br>');
              }
            }
            if (cardParams.isCloze.value) {
              note = AnkiConnectController()
                  .convertCloze(note, [cardParams.clozeType.value]);
            }
            final parts = obj['deckName'].split('::');
            final sourceLabel =
                "${bookName} ${parts.length > 1 ? parts.sublist(1).join('->') : parts.join('')}";
            String sourceLink =
                '<a href="weread://reading?bId=${book.value}&style=1">${sourceLabel}</a>';
            if (obj['type'] == 'note') {
              sourceLink =
                  '<a href="weread://reviewDetail?reviewId=${obj["id"]}&reviewType=1&style=1&isFromBook=0&isLike=0&s=book_review&promoteId=book_review">${sourceLabel}</a>';
            }
            notes.add(AnkiNote(
              deckName: cardParams.isCreateSubDeck.value
                  ? obj['deckName']
                  : cardParams.parentDeck.value,
              modelName: "Kevin Reader Card v2",
              fields: [
                obj['markText'], // 原文
                sourceLink,
                "anki.wereader_card.published_at"
                    .trParams({'time': createTime}), // 备注
                header, // 标题
                note, // 笔记
                reverse, // 反转
              ],
              tags: tags,
              guid: obj['id'].toString(),
            ));
          }

          // 根据导出模式处理
          final exportMode = settingController.getExportCardMode();
          if (exportMode == "apkg") {
            final outputPath = await PathUtils.getOutputApkgPath();
            final resp =
                await AnkiConnectController().genApkg(notes, [], outputPath);
            if (resp.status == "success") {
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              progressController.outputPath.value = resp.data;
            } else {
              progressController.updateProgress(
                status: "error",
                message: resp.message,
                current: 0.0,
                total: 100.0,
              );
            }
          } else if (exportMode == "ankiconnect") {
            final outputPath = await PathUtils.getOutputApkgPath();
            final resp =
                await AnkiConnectController().genApkg(notes, [], outputPath);
            if (resp.status == "success") {
              await AnkiConnectController()
                  .importApkg(resp.data, isDelete: true);
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
            } else {
              progressController.updateProgress(
                status: "error",
                message: resp.message,
                current: 0.0,
                total: 100.0,
              );
            }
          }
        }
      } else {
        progressController.reset(
          showOutputHint: true,
          numberButtons: 2,
        );
        progressController.showProgressDialog(context);
        if (exportParams.source.value == 'hot') {
          final highlights = await extractHotHighlights(
              exportParams.hotSortMethod.value,
              exportParams.sortAscending.value);
          logger.i(highlights);
          progressController.updateProgress(
              status: "running",
              current: 10,
              total: 100,
              message: "anki.wereader_card.processing".tr);
          // 根据导出格式生成内容
          switch (exportParams.exportFormat.value) {
            case 'markdown':
              String content = '';
              for (var highlight in highlights) {
                content +=
                    '${'#' * int.parse(highlight['level'].toString())} ${highlight['title']}\n\n';
                content += '> ${highlight['markText']}\n\n';
                content += 'anki.wereader_card.people_highlighted'.trParams(
                        {'count': highlight['totalCount'].toString()}) +
                    '\n\n';
                content += '---\n\n';
              }
              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.md';
              logger.i(path);
              await File(path).writeAsString(content);
              progressController.outputPath.value = path;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              break;
            case 'json':
              String content = jsonEncode(highlights);
              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.json';
              logger.i(path);
              // 保存文件
              await File(path).writeAsString(content);
              progressController.outputPath.value = path;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              break;
            case 'csv':
              // 准备数据
              final rows = <List<dynamic>>[];

              // 添加表头
              rows.add([
                'chapterUid',
                'level',
                'title',
                'markText',
                'totalCount',
              ]);

              // 添加数据行
              for (var highlight in highlights) {
                rows.add([
                  highlight['chapterUid'].toString(),
                  highlight['level'].toString(),
                  highlight['title'],
                  highlight['markText'],
                  highlight['totalCount'].toString(),
                ]);
              }

              // 转换为CSV字符串
              final csvString = const ListToCsvConverter().convert(rows);

              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.csv';
              logger.i(path);

              // 保存文件
              await File(path).writeAsString(csvString);
              progressController.outputPath.value = path;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              break;
            case 'xlsx':
              var excel = Excel.createExcel();
              var sheet = excel['Sheet1'];

              // Add headers
              sheet.insertRowIterables(
                [
                  TextCellValue('chapterUid'),
                  TextCellValue('level'),
                  TextCellValue('title'),
                  TextCellValue('markText'),
                  TextCellValue('totalCount'),
                ],
                0,
              );
              // Add data rows
              for (var i = 0; i < highlights.length; i++) {
                var highlight = highlights[i];
                sheet.insertRowIterables(
                  [
                    TextCellValue(highlight['chapterUid'].toString()),
                    TextCellValue(highlight['level'].toString()),
                    TextCellValue(highlight['title']),
                    TextCellValue(highlight['markText']),
                    TextCellValue(highlight['totalCount'].toString()),
                  ],
                  i + 1,
                );
              }
              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.xlsx';
              logger.i(path);
              // 保存Excel文件
              var bytes = excel.save();
              if (bytes != null) {
                await File(path).writeAsBytes(bytes);
                progressController.outputPath.value = path;
                progressController.updateProgress(
                  status: "completed",
                  message: 'common.completed'.tr,
                  current: 100.0,
                  total: 100.0,
                );
              } else {
                throw 'anki.wereader_card.generate_excel_failed'.tr;
              }
              break;
          }
        } else {
          final objs = await extractMyNotes(
              exportParams.exportCategory.contains("line"),
              exportParams.exportCategory.contains("note"),
              exportParams.mySortMethod.value,
              exportParams.sortAscending.value);
          logger.i(objs);
          progressController.updateProgress(
              status: "running",
              current: 10,
              total: 100,
              message: "anki.wereader_card.processing".tr);
          switch (exportParams.exportFormat.value) {
            case 'markdown':
              String content = '';
              for (var obj in objs) {
                final timeStr = DateTime.fromMillisecondsSinceEpoch(
                        obj['createTime'] * 1000)
                    .toString()
                    .split('.')[0];
                content +=
                    '${'#' * int.parse(obj['level'].toString())} ${obj['title']}\n\n';
                content += '> ${obj['markText']}\n\n';
                if (obj['type'] == 'note') {
                  content += 'anki.wereader_card.published_thought_at'
                          .trParams({'time': timeStr}) +
                      '\n\n';
                  content += '${obj['comment']}\n\n';
                } else {
                  content += 'anki.wereader_card.added_highlight_at'
                          .trParams({'time': timeStr}) +
                      '\n\n';
                }
                content += '---\n\n';
              }
              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.md';
              logger.i(path);
              await File(path).writeAsString(content);
              progressController.outputPath.value = path;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              break;
            case 'json':
              String content = jsonEncode(objs);
              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.json';
              logger.i(path);
              // 保存文件
              await File(path).writeAsString(content);
              progressController.outputPath.value = path;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              break;
            case 'csv':
              // 准备数据
              final rows = <List<dynamic>>[];

              // 添加表头
              rows.add([
                'createTime',
                'chapterUid',
                'level',
                'title',
                'markText',
                'comment',
              ]);

              // 添加数据行
              for (var obj in objs) {
                rows.add([
                  DateTime.fromMillisecondsSinceEpoch(obj['createTime'] * 1000)
                      .toString(),
                  obj['chapterUid'].toString(),
                  obj['level'].toString(),
                  obj['title'],
                  obj['markText'],
                  obj['comment'].toString(),
                ]);
              }

              // 转换为CSV字符串
              final csvString = const ListToCsvConverter().convert(rows);

              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.csv';
              logger.i(path);

              // 保存文件
              await File(path).writeAsString(csvString);
              progressController.outputPath.value = path;
              progressController.updateProgress(
                status: "completed",
                message: 'common.completed'.tr,
                current: 100.0,
                total: 100.0,
              );
              break;
            case 'xlsx':
              var excel = Excel.createExcel();
              var sheet = excel['Sheet1'];

              // Add headers
              sheet.insertRowIterables(
                [
                  TextCellValue('createTime'),
                  TextCellValue('chapterUid'),
                  TextCellValue('level'),
                  TextCellValue('title'),
                  TextCellValue('markText'),
                  TextCellValue('comment'),
                ],
                0,
              );
              // Add data rows
              for (var i = 0; i < objs.length; i++) {
                var obj = objs[i];
                sheet.insertRowIterables(
                  [
                    TextCellValue(DateTime.fromMillisecondsSinceEpoch(
                            obj['createTime'] * 1000)
                        .toString()),
                    TextCellValue(obj['chapterUid'].toString()),
                    TextCellValue(obj['level'].toString()),
                    TextCellValue(obj['title']),
                    TextCellValue(obj['markText']),
                    TextCellValue(obj['comment'].toString()),
                  ],
                  i + 1,
                );
              }
              // 生成文件名
              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final path =
                  '${exportParams.outputDir.value}/wereader_${book.value}_$timestamp.xlsx';
              logger.i(path);
              // 保存Excel文件
              var bytes = excel.save();
              if (bytes != null) {
                await File(path).writeAsBytes(bytes);
                progressController.outputPath.value = path;
                progressController.updateProgress(
                  status: "completed",
                  message: 'common.completed'.tr,
                  current: 100.0,
                  total: 100.0,
                );
              } else {
                throw 'anki.wereader_card.generate_excel_failed'.tr;
              }
              break;
          }
        }
      }
    } catch (e) {
      logger.e("Error submitting: $e");
      progressController.updateProgress(
        status: "error",
        message: e.toString(),
        current: 0.0,
        total: 100.0,
      );
    }
  }
}
