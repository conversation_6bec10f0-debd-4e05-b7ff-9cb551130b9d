import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFOCRPageController extends GetxController {
  // 表单参数
  final isMergeMode = false.obs;
  final isShowPageSep = true.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  // OCR 设置
  final ocrProvider = "paddle-ocr".obs;
  final modelName = "".obs;
  final apiKey = "".obs;
  final baseUrl = "".obs;
  final systemPrompt = "".obs;
  final mergeOutput = false.obs;

  void submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.process.failed'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      for (String filePath in selectedFilePaths) {
        try {
          progressController.updateProgress(
              status: "running",
              message:
                  "${'toolbox.common.process.running'.tr}",
              current: 1.0,
              total: 100.0);

          // Determine output directory
          final pathUtils = PathUtils(filePath);
          String outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_ocr",
            suffix: "",
            outputDir: outputDir.value,
          );

          // Create output directory if it doesn't exist
          final outputDirectory = Directory(PathUtils(outputPath).parent);
          if (!await outputDirectory.exists()) {
            await outputDirectory.create(recursive: true);
          }

          progressController.outputPath.value = outputPath;

          // Perform OCR on PDF
          await _performPDFOCR(filePath, outputPath);
        } catch (e) {
          logger.e("Error processing file $filePath: $e");
          progressController.updateProgress(
            status: "error",
            message: "${'toolbox.common.process.processFailed'.tr}: $e",
          );
          return;
        }
      }

      progressController.updateProgress(
          status: "completed", message: 'common.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error",
        message: "${'toolbox.common.process.failed'.tr}: $e",
      );
    }
  }

  /// Perform OCR on PDF file page by page
  Future<void> _performPDFOCR(String pdfPath, String outputPath) async {
    try {
      // Create temporary directory for images
      final tempDirPath = PathUtils.join([
        await PathUtils.tempDir,
        'ocr_images_${DateTime.now().millisecondsSinceEpoch}'
      ]);
      final tempDir = Directory(tempDirPath);
      await tempDir.create(recursive: true);

      final convertData = {
        "path": pdfPath,
        "output_path": tempDirPath,
        "page_range": pageRange.value.isEmpty ? "all" : pageRange.value,
        "output_format": "png",
        "dpi": 300,
        "is_gray": false,
        "show_progress": false
      };
      final convertResp =
          await messageController.request(convertData, 'pdf_to_image');
      logger.w("convertResp: $convertResp");
      if (convertResp.status != "success") {
        throw Exception(
            "Failed to convert PDF to images: ${convertResp.message}");
      }

      // Parse the conversion result to get image paths
      // The result is a JSON string containing a map of page numbers to file paths
      final Map<String, dynamic> imageResults = convertResp.data is String
          ? jsonDecode(convertResp.data)
          : convertResp.data as Map<String, dynamic>;

      if (imageResults.isEmpty) {
        throw Exception("No images generated from PDF");
      }

      // Extract image paths and sort by page number
      List<String> imagePaths = [];
      final sortedKeys = imageResults.keys.toList()
        ..sort((a, b) => int.parse(a).compareTo(int.parse(b)));

      for (String pageNum in sortedKeys) {
        final imagePath = imageResults[pageNum];
        if (imagePath is String && imagePath.isNotEmpty) {
          imagePaths.add(imagePath);
        }
      }

      if (imagePaths.isEmpty) {
        throw Exception("No valid image paths found");
      }

      // Parse page range to get actual page numbers
      final actualPageNumbers =
          _parseActualPageNumbers(pageRange.value, imagePaths.length);

      // Setup output directory structure
      final outputDir = PathUtils(outputPath).parent;
      final baseName = PathUtils(outputPath).stem;
      final pdfSubDir = PathUtils.join([outputDir, baseName]);
      await PathUtils(pdfSubDir).makeDirs();

      // Initialize merged file if in merge mode
      File? mergedFile;
      if (isMergeMode.value) {
        mergedFile = File(PathUtils.join([pdfSubDir, "merged_ocr.txt"]));
        // Create empty file or clear existing content
        await mergedFile.writeAsString("", encoding: utf8);
      }

      // Process each page incrementally
      await _processOCRIncremental(
          imagePaths, actualPageNumbers, pdfSubDir, mergedFile);

      // Clean up temporary image files and directory
      try {
        if (await tempDir.exists()) {
          await tempDir.delete(recursive: true);
        }
      } catch (e) {
        logger.w("Failed to delete temporary directory ${tempDir.path}: $e");
      }
    } catch (e) {
      logger.e("PDF OCR processing failed: $e");
      rethrow;
    }
  }

  /// Parse page range to get actual page numbers (1-based)
  List<int> _parseActualPageNumbers(String pageRange, int totalPages) {
    if (pageRange.isEmpty || pageRange.toLowerCase() == 'all') {
      // Return sequential page numbers starting from 1
      return List.generate(totalPages, (index) => index + 1);
    }

    // Use the existing parsePageRange function but convert to 1-based
    final zeroBasedPages = parsePageRange(pageRange, totalPages);
    return zeroBasedPages.map((page) => page + 1).toList();
  }

  /// Process OCR incrementally, saving each page immediately after processing
  Future<void> _processOCRIncremental(List<String> imagePaths,
      List<int> actualPageNumbers, String outputDir, File? mergedFile) async {
    final totalPages = imagePaths.length;
    int successCount = 0;

    for (int i = 0;
        i < imagePaths.length && i < actualPageNumbers.length;
        i++) {
      final imagePath = imagePaths[i];
      final actualPageNumber = actualPageNumbers[i];

      try {
        // Update progress for current page
        progressController.updateProgress(
          status: "running",
          message: "正在识别页面 $actualPageNumber (${i + 1}/$totalPages)...",
          current: (i+1).toDouble(),
          total: totalPages.toDouble(),
        );

        // Perform OCR on single image
        final ocrData = {
          "file_paths": [imagePath],
          "provider": ocrProvider.value,
          "model_name": modelName.value,
          "api_key": apiKey.value,
          "base_url": baseUrl.value,
          "system_prompt": systemPrompt.value,
          "merge_output": false,
          "response_format": "json",
          "show_progress": false // Don't show progress for individual pages
        };

        final ocrResp = await messageController.request(ocrData, 'anki/ocr');

        if (ocrResp.status != "success") {
          logger.e("OCR failed for page $actualPageNumber: ${ocrResp.message}");
          continue; // Skip this page but continue with others
        }

        // Parse OCR result
        final List<dynamic> ocrResults = (ocrResp.data is String)
            ? jsonDecode(ocrResp.data)
            : (ocrResp.data as List);

        if (ocrResults.isEmpty) {
          logger.w("Empty OCR result for page $actualPageNumber");
          continue;
        }

        // Extract text from OCR result
        final result = ocrResults[0]; // Single page result
        String ocrText = "";

        if (result is String) {
          final Map<String, dynamic> resultJson = jsonDecode(result);
          ocrText = _extractTextFromOCRResult(resultJson);
        } else if (result is Map<String, dynamic>) {
          ocrText = _extractTextFromOCRResult(result);
        } else {
          logger.w(
              "Unexpected OCR result format for page $actualPageNumber: ${result.runtimeType}");
          continue;
        }

        // Save immediately based on mode
        await _savePageResultImmediately(
            ocrText, actualPageNumber, outputDir, mergedFile);
        successCount++;

        // Update progress for successful save
        progressController.updateProgress(
          status: "running",
          message: "保存页面 $actualPageNumber ($successCount/$totalPages 完成)",
        );
      } catch (e) {
        logger.e("Failed to process page $actualPageNumber: $e");
        // Continue with next page
      }
    }

    logger.i(
        "OCR processing completed. Successfully processed $successCount out of $totalPages pages.");
  }

  /// Save a single page result immediately based on the current mode
  Future<void> _savePageResultImmediately(String ocrText, int actualPageNumber,
      String outputDir, File? mergedFile) async {
    try {
      if (isMergeMode.value && mergedFile != null) {
        // Append to merged file
        String pageContent;
        if (isShowPageSep.value) {
          pageContent = "--- Page $actualPageNumber ---\n$ocrText\n\n";
        } else {
          pageContent = "$ocrText\n\n";
        }
        await mergedFile.writeAsString(pageContent,
            mode: FileMode.append, encoding: utf8);
        logger.i(
            "Appended page $actualPageNumber to merged file: ${mergedFile.path}");
      } else {
        // Save as individual file
        final outputFileName = "page_$actualPageNumber.txt";
        final outputFile = File(PathUtils.join([outputDir, outputFileName]));
        await outputFile.writeAsString(ocrText, encoding: utf8);
        logger.i("Saved page $actualPageNumber to: ${outputFile.path}");
      }
    } catch (e) {
      logger.e("Failed to save page $actualPageNumber: $e");
      rethrow;
    }
  }

  /// Extract text from OCR result JSON
  String _extractTextFromOCRResult(Map<String, dynamic> resultJson) {
    try {
      if (resultJson.containsKey('error')) {
        return "OCR Error: ${resultJson['error']}";
      }

      if (resultJson.containsKey('blocks') && resultJson['blocks'] is List) {
        final List<dynamic> blocks = resultJson['blocks'];
        return blocks
            .where((block) =>
                block is Map<String, dynamic> && block.containsKey('text'))
            .map((block) => block['text'].toString())
            .join('\n');
      }

      // Fallback: try to find any text field
      if (resultJson.containsKey('text')) {
        return resultJson['text'].toString();
      }

      return "No text found in OCR result";
    } catch (e) {
      logger.e("Failed to extract text from OCR result: $e");
      return "Error extracting text: $e";
    }
  }
}
