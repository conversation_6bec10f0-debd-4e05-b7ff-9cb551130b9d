{"about.bilibili": "Bilibili", "about.checkUpdate": "Check for Updates", "about.communication": "Community Channels", "about.copyright": "Copyright © 2023-2025 Kevin2li", "about.friendlyLinks": "Friendly Links", "about.github": "<PERSON><PERSON><PERSON>", "about.officialWebsite": "Official Website", "about.paidUsersOnly": "For paid users only. Please include 'Platform + Order ID' in your join request, e.g., Taobao xx.", "about.privacyPolicy": "Privacy Policy", "about.qqChannel": "QQ Channel", "about.qqGroup": "QQ Group", "about.scanQrCode": "Scan the QR code below to join", "about.termsOfService": "Terms of Service", "about.title": "About and Help", "about.version": "Version", "about.wechatGroup": "WeChat Group", "activate.activate": "Activate", "activate.activate_time": "Activation Time", "activate.activated": "Activated", "activate.activationCode": "Activation Code", "activate.activationInfo": "Activation Information", "activate.allowed_divices": "Allowed Devices", "activate.buyActivationCode": "Buy Activation Code?", "activate.deactivate.cancel": "Cancel", "activate.deactivate.confirm": "Confirm Deactivation", "activate.deactivate.description": "Please note:\n1. Deactivation will release the activation slot for the current device.\n2. After deactivation, you cannot use the same activation code on this device again unless you reinstall the OS.\nAre you sure you want to deactivate this device?", "activate.deactivate.title": "Deactivate Device", "activate.enterActivationCode": "Please enter your activation code", "activate.faq": "FAQ", "activate.not_activated": "Not Activated", "activate.purchase.goToWebsite": "Go to Website", "activate.purchase.officialWebsite": "Official Website:", "activate.purchase.onlineStore": "Online Store:", "activate.purchase.pdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activate.purchase.taobao": "Taobao", "activate.purchase.title": "Purchase Activation Code", "activate.requestTrial": "Request Trial", "activate.state": "Activation Status", "activate.title": "Software Activation", "activate.trialRequest.cancel": "Cancel", "activate.trialRequest.confirm": "Confirm", "activate.trialRequest.description": "The trial period is 3 days, during which you can experience all features. After the trial ends, you need to purchase an activation code to continue.", "activate.trialRequest.title": "Request Trial", "activate.unregister_device": "Deactivate This Device", "anki.card_media_manager.at_least_one_card_type": "Select at least one media type", "anki.card_media_manager.compressing_images": "Compressing images", "anki.card_media_manager.compression_completed": "Compression completed: @success/@total files successful", "anki.card_media_manager.compression_failed": "Image compression failed: @error", "anki.card_media_manager.compression_source": "Source", "anki.card_media_manager.compression_tab": "Image Compression", "anki.card_media_manager.copying_file": "Copying file: @current/@total - @filename", "anki.card_media_manager.deck": "Deck", "anki.card_media_manager.deck_compression_completed": "Deck compression completed: @success/@total files successful", "anki.card_media_manager.deck_compression_completed_with_updates": "Deck compression completed: compressed @compressed images, updated @updated/@total cards", "anki.card_media_manager.deck_compression_failed": "Deck compression failed: @error", "anki.card_media_manager.description": "Supports compressing images in decks, automatically uploading deck images to image hosting, extracting images from decks, etc.", "anki.card_media_manager.failed_to_get_media_path": "Failed to get Anki media path", "anki.card_media_manager.file": "File", "anki.card_media_manager.function_description": "Function Description", "anki.card_media_manager.getting_deck_cards": "Getting deck cards", "anki.card_media_manager.include_audio": "Include Audio", "anki.card_media_manager.include_network_image": "Include Network Images", "anki.card_media_manager.include_video": "Include Video", "anki.card_media_manager.initializing": "Initializing", "anki.card_media_manager.invalid_quality": "Invalid compression quality, please enter a value between 1-100", "anki.card_media_manager.invalid_source_mode": "Invalid source mode", "anki.card_media_manager.media_type_local_audio": "Local Audio", "anki.card_media_manager.media_type_local_image": "Local Images", "anki.card_media_manager.media_type_local_video": "Local Video", "anki.card_media_manager.media_type_network_audio": "Network Audio", "anki.card_media_manager.media_type_network_image": "Network Images", "anki.card_media_manager.media_type_network_video": "Network Video", "anki.card_media_manager.media_types": "Media Types", "anki.card_media_manager.no_cards_found": "No cards found", "anki.card_media_manager.no_deck_selected": "No deck selected", "anki.card_media_manager.no_files_selected": "No files selected", "anki.card_media_manager.no_media_found": "No media files found", "anki.card_media_manager.no_output_dir": "No output directory set", "anki.card_media_manager.no_valid_files": "No valid files", "anki.card_media_manager.picgo_address": "PicGo Address", "anki.card_media_manager.picgo_address_cannot_empty": "Address cannot be empty", "anki.card_media_manager.picgo_address_placeholder": "Enter PicGo connection address", "anki.card_media_manager.quality": "Compression Quality", "anki.card_media_manager.quality_error": "Please enter a positive integer", "anki.card_media_manager.quality_placeholder": "Set compression quality, between 0~100", "anki.card_media_manager.scan_completed": "Scan completed: @success/@total files successful, @failed failed", "anki.card_media_manager.scan_failed": "Image scan failed", "anki.card_media_manager.scan_tab": "Scan Media", "anki.card_media_manager.scanning_cards": "Scanning cards: @current/@total", "anki.card_media_manager.scanning_deck": "Scanning deck", "anki.card_media_manager.title": "Media Manager", "anki.card_media_manager.updating_note_references": "Updating note references: @current/@total", "anki.card_media_manager.upload_completed": "Upload completed: @success/@total files successful, @failed failed", "anki.card_media_manager.upload_failed": "Image upload failed: @error", "anki.card_media_manager.upload_failed_no_files_uploaded": "All files failed to upload", "anki.card_media_manager.upload_tab": "Upload to Image Host", "anki.card_media_manager.uploading_file": "Uploading file: @current/@total - @filename", "anki.common.a_file": "Answer file", "anki.common.advanced_options": "Advanced options", "anki.common.answer_cloze_grammar": "C<PERSON>ze deletion grammar for answers", "anki.common.card_mode": "Card creation mode", "anki.common.create_subdeck": "Create subdeck", "anki.common.export_mode": "Export Mode", "anki.common.is_answer_cloze": "<PERSON><PERSON><PERSON> deletion for answers", "anki.common.page_range": "Page range", "anki.common.q_file": "Question file", "anki.common.refresh_success": "Refresh successful", "anki.common.show_source": "Show source", "anki.common.tags": "Tags", "anki.common.target_deck": "Target Deck", "anki.deck_manager.align_bottom": "Bottom Align", "anki.deck_manager.align_center": "Center Align", "anki.deck_manager.align_top": "Top Align", "anki.deck_manager.alignment": "Alignment", "anki.deck_manager.break_in_line": "Line Break", "anki.deck_manager.cards_count_invalid": "Please enter an integer", "anki.deck_manager.cards_count_placeholder": "Enter card count", "anki.deck_manager.cards_count_required": "Please enter card count, e.g.: 5", "anki.deck_manager.cards_per_file": "Cards per file", "anki.deck_manager.cards_per_row": "Cards per row", "anki.deck_manager.cell_margin": "Cell margin", "anki.deck_manager.cell_margin_invalid": "Please enter valid CSS margin format, e.g.: 10px or 5px 10px 15px 20px", "anki.deck_manager.cell_margin_placeholder": "Enter margin, e.g.: 10px or 5px 10px 15px 20px", "anki.deck_manager.cell_margin_required": "Please enter margin, e.g.: 10px or 5px 10px 15px 20px", "anki.deck_manager.cell_padding": "Cell padding", "anki.deck_manager.cell_padding_invalid": "Please enter valid CSS padding format, e.g.: 10px or 5px 10px 15px 20px", "anki.deck_manager.cell_padding_placeholder": "Enter padding, e.g.: 10px or 5px 10px 15px 20px", "anki.deck_manager.cell_padding_required": "Please enter padding, e.g.: 10px or 5px 10px 15px 20px", "anki.deck_manager.clone_deck": "<PERSON><PERSON>", "anki.deck_manager.create_deck": "Create Deck", "anki.deck_manager.create_mode": "Creation Mode", "anki.deck_manager.create_mode_file": "File Import", "anki.deck_manager.create_mode_manual": "Manual Entry", "anki.deck_manager.custom_font_size": "Custom Font Size", "anki.deck_manager.deck_list": "Deck List", "anki.deck_manager.deck_list_placeholder": "Enter deck names, one per line", "anki.deck_manager.deck_list_required": "Deck list cannot be empty", "anki.deck_manager.deck_names_file": "Deck Names File", "anki.deck_manager.errors.export_csv_failed": "Failed to export CSV", "anki.deck_manager.errors.export_html_failed": "Failed to export HTML: @error", "anki.deck_manager.errors.export_json_failed": "Failed to export JSON", "anki.deck_manager.errors.export_xlsx_failed": "Failed to export Xlsx", "anki.deck_manager.errors.select_deck": "Please select a deck", "anki.deck_manager.errors.select_deck_file": "Please select deck file", "anki.deck_manager.errors.select_deck_to_remove": "Please select decks to remove", "anki.deck_manager.errors.select_output_dir": "Please select output directory", "anki.deck_manager.errors.select_source_dest_deck": "Please select source and destination decks", "anki.deck_manager.errors.unsupported_export_mode": "Unsupported export mode: @mode", "anki.deck_manager.export_deck": "Export Deck", "anki.deck_manager.export_format": "Export Format", "anki.deck_manager.export_mode": "Export Mode", "anki.deck_manager.export_mode_qa_different_file": "Questions and answers in separate files", "anki.deck_manager.export_mode_qa_merge": "Questions and answers merged", "anki.deck_manager.export_mode_qa_same_page": "Questions and answers on same page", "anki.deck_manager.export_modes.qa_different_file": "Questions and Answers in Different Files", "anki.deck_manager.export_modes.qa_merge": "Questions and Answers Merged", "anki.deck_manager.export_modes.qa_same_page": "Questions and Answers Side by Side", "anki.deck_manager.feature_description": "Supports batch deck creation, import, cloning, and export", "anki.deck_manager.font_size_invalid": "Please enter a decimal number", "anki.deck_manager.font_size_placeholder": "Enter font size", "anki.deck_manager.font_size_required": "Please enter font size, e.g.: 12", "anki.deck_manager.function_description": "Function Description", "anki.deck_manager.html.answer_header": "Answer", "anki.deck_manager.html.answer_style_comment": "Card @cardId answer styles", "anki.deck_manager.html.answer_title": "Answers @fileIndex", "anki.deck_manager.html.card_style_comment": "Card @cardId styles", "anki.deck_manager.html.card_title": "Cards @fileIndex", "anki.deck_manager.html.question_header": "Question", "anki.deck_manager.html.question_style_comment": "Card @cardId question styles", "anki.deck_manager.html.question_title": "Questions @fileIndex", "anki.deck_manager.import_deck": "Import Deck", "anki.deck_manager.include_sched": "Include Review Progress", "anki.deck_manager.messages.clone_deck_success": "<PERSON> cloned successfully", "anki.deck_manager.messages.create_deck_success": "Deck created successfully", "anki.deck_manager.messages.delete_deck_success": "Deck deleted successfully", "anki.deck_manager.messages.delete_empty_deck_success": "Empty decks deleted successfully", "anki.deck_manager.messages.export_csv_success": "Successfully exported @count notes to CSV file", "anki.deck_manager.messages.export_deck_success": "Deck exported successfully", "anki.deck_manager.messages.export_html_success": "HTML exported successfully", "anki.deck_manager.messages.export_json_success": "Successfully exported @count notes to JSON file", "anki.deck_manager.messages.export_xlsx_success": "Successfully exported @count notes to Xlsx file", "anki.deck_manager.messages.generating_html": "Generating HTML files...", "anki.deck_manager.messages.generating_html_progress": "Generating HTML file (@current/@total)", "anki.deck_manager.messages.get_media_dir_failed": "Failed to get media directory", "anki.deck_manager.messages.import_deck_success": "Deck imported successfully", "anki.deck_manager.messages.import_from_file_success": "Deck imported from file successfully", "anki.deck_manager.messages.processing": "Processing...", "anki.deck_manager.messages.querying_cards": "Querying cards...", "anki.deck_manager.messages.sorting_cards": "Sorting cards...", "anki.deck_manager.output_directory": "Output Directory", "anki.deck_manager.remove_deck": "Remove Deck", "anki.deck_manager.remove_deck_label": "Remove Deck", "anki.deck_manager.remove_deck_placeholder": "Select deck to remove", "anki.deck_manager.remove_mode": "Removal Mode", "anki.deck_manager.remove_mode_empty": "Remove Empty Decks", "anki.deck_manager.remove_mode_manual": "Manual Removal", "anki.deck_manager.remove_range": "Removal Range", "anki.deck_manager.remove_range_all": "All Decks", "anki.deck_manager.remove_range_part": "Specified Decks", "anki.deck_manager.sort_direction": "Sort Direction", "anki.deck_manager.sort_direction_asc": "Ascending", "anki.deck_manager.sort_direction_desc": "Descending", "anki.deck_manager.sort_direction_random": "Random", "anki.deck_manager.sort_method": "Sort Method", "anki.deck_manager.sort_method_add_time": "Creation Time", "anki.deck_manager.sort_method_due_time": "Due Time", "anki.deck_manager.sort_method_field": "Sort Field", "anki.deck_manager.sort_method_lapses": "Lapse Count", "anki.deck_manager.sort_method_modify_time": "Modification Time", "anki.deck_manager.sort_method_reviews": "Review Count", "anki.deck_manager.source_deck": "Source Deck", "anki.deck_manager.source_deck_placeholder": "Select source deck", "anki.deck_manager.source_deck_search_placeholder": "Enter source deck", "anki.deck_manager.target_deck_input_placeholder": "Enter target deck", "anki.deck_manager.target_deck_label": "Target Deck", "anki.deck_manager.target_deck_placeholder": "Enter target deck", "anki.deck_manager.target_deck_required": "Please enter target deck", "anki.deck_manager.target_deck_select_placeholder": "Select target deck", "anki.deck_manager.title": "Deck Manager", "anki.excel_card.answer_in_column": "Answer in separate column", "anki.excel_card.answer_in_text": "Answer in question text", "anki.excel_card.answer_pattern": "Answer Pattern", "anki.excel_card.answer_position": "Answer Position", "anki.excel_card.choice_tab": "Multiple Choice", "anki.excel_card.correct_answer_pattern": "Correct Answer Pattern", "anki.excel_card.excel_card_title": "Excel Flashcards", "anki.excel_card.excel_file": "Excel File", "anki.excel_card.feature_description": "Supports creating Q&A, multiple choice, and true/false cards from Excel", "anki.excel_card.field_mapping": "Field Mapping", "anki.excel_card.free_match": "Free match", "anki.excel_card.function_description": "Function Description", "anki.excel_card.guru_import": "Guru <PERSON>", "anki.excel_card.id_column": "ID Column", "anki.excel_card.input_answer_pattern": "Enter answer pattern", "anki.excel_card.input_correct_answer_pattern": "Enter correct answer pattern", "anki.excel_card.input_option_pattern": "Enter option pattern", "anki.excel_card.input_tags": "Enter tags", "anki.excel_card.input_template": "Enter template", "anki.excel_card.input_wrong_answer_pattern": "Enter wrong answer pattern", "anki.excel_card.judge_tab": "True/False", "anki.excel_card.no_column_info": "No column information in Excel file", "anki.excel_card.option_in_column": "Options in separate columns", "anki.excel_card.option_in_text": "Options in single column", "anki.excel_card.option_pattern": "Option Pattern", "anki.excel_card.option_position": "Option Position", "anki.excel_card.qa_tab": "Q&A", "anki.excel_card.read_excel_failed": "Failed to read Excel file: @error", "anki.excel_card.select_answer_pattern": "Select answer pattern", "anki.excel_card.select_column": "Select column", "anki.excel_card.select_correct_answer_pattern": "Select correct answer pattern", "anki.excel_card.select_excel_file": "Select Excel file", "anki.excel_card.select_excel_file_error": "Please select an Excel file", "anki.excel_card.select_file_dialog": "Select File", "anki.excel_card.select_id_column_placeholder": "Select ID column as unique card ID for updates", "anki.excel_card.select_option_pattern": "Select option pattern", "anki.excel_card.select_sub_deck_column": "Select subdeck column", "anki.excel_card.select_tag_column": "Select tag column", "anki.excel_card.select_tags": "Select tags", "anki.excel_card.select_template": "Select template", "anki.excel_card.select_worksheet": "Select worksheet", "anki.excel_card.select_wrong_answer_pattern": "Select wrong answer pattern", "anki.excel_card.sub_deck_column": "Subdeck Column", "anki.excel_card.table_column": "Table Column", "anki.excel_card.tag_column": "Tag Column", "anki.excel_card.template": "Template", "anki.excel_card.template_field": "Template Field", "anki.excel_card.worksheet": "Worksheet", "anki.excel_card.wrong_answer_pattern": "Wrong Answer Pattern", "anki.flash.choose_save_location_and_filename": "Choose save location and filename", "anki.flash.copy_suffix": "(Copy)", "anki.flash.editor.autoSaveFailed": "Auto-save failed: @error", "anki.flash.editor.bold": "Bold", "anki.flash.editor.cancel": "Cancel", "anki.flash.editor.cannotLoadContent": "Cannot load original content, please re-edit.", "anki.flash.editor.codeBlock": "Code Block", "anki.flash.editor.confirm": "Confirm", "anki.flash.editor.contentEmpty": "Note content is empty, deleting note", "anki.flash.editor.deleteEmptyNoteFailed": "Failed to delete empty note: @error", "anki.flash.editor.divider": "Add Divider", "anki.flash.editor.export": "Export", "anki.flash.editor.exportFailed": "Export failed: @error", "anki.flash.editor.exportFailedCannotSave": "Export failed: Cannot save file: @error", "anki.flash.editor.exportFailedEmpty": "Export failed: Document content is empty", "anki.flash.editor.exportFailedEmptyResult": "Export failed: Conversion result is empty", "anki.flash.editor.exportSuccess": "Export successful: File saved to: @path", "anki.flash.editor.generatingMindMap": "Generating mind map...", "anki.flash.editor.hyperlink": "Hyperlink", "anki.flash.editor.image": "Add Image", "anki.flash.editor.imageDialogTitle": "Insert Image", "anki.flash.editor.imageSelectError": "Error: Cannot select image: @error", "anki.flash.editor.imageSelectLocal": "Select Local Image", "anki.flash.editor.imageUrl": "Image URL", "anki.flash.editor.imageUrlPlaceholder": "https://example.com/image.jpg", "anki.flash.editor.insertImage": "Insert Image", "anki.flash.editor.insertLink": "Insert Link", "anki.flash.editor.italic": "Italic", "anki.flash.editor.linkCannotOpen": "Cannot open link: @url", "anki.flash.editor.linkDialogTitle": "Insert Link", "anki.flash.editor.linkOpenError": "Error opening link: @error", "anki.flash.editor.linkUrl": "URL", "anki.flash.editor.linkUrlPlaceholder": "https://example.com", "anki.flash.editor.loadingImage": "Loading image...", "anki.flash.editor.mindMapCannotGenerate": "Cannot generate mind map, please check Markdown format", "anki.flash.editor.mindMapGenerationFailed": "Mind map generation failed", "anki.flash.editor.mindMapMode": "Mind Map", "anki.flash.editor.noteTitle": "Note Title", "anki.flash.editor.noteTitleEmpty": "Error: Note title cannot be empty", "anki.flash.editor.orderedList": "Ordered List", "anki.flash.editor.placeholder": "Edit note content here...", "anki.flash.editor.quote": "Quote", "anki.flash.editor.redo": "Redo", "anki.flash.editor.returnToEditor": "Return to Editor", "anki.flash.editor.save": "Save", "anki.flash.editor.saveDialogTitle": "Save Markdown File", "anki.flash.editor.saveFailed": "Save failed: @error", "anki.flash.editor.saveSuccess": "Note saved", "anki.flash.editor.strikethrough": "Strikethrough", "anki.flash.editor.underline": "Underline", "anki.flash.editor.undo": "Undo", "anki.flash.editor.unorderedList": "Unordered List", "anki.flash.editor.untitledNote": "Untitled Note", "anki.flash.errors.copy_note_index_out_of_bounds": "Failed to copy note: index out of bounds", "anki.flash.errors.delete_note_index_out_of_bounds": "Failed to delete note: index out of bounds", "anki.flash.errors.export_note_index_out_of_bounds": "Failed to export note: index out of bounds", "anki.flash.errors.export_to_anki_failed": "Failed to export note to Anki", "anki.flash.errors.export_to_markdown_failed": "Failed to export note to Markdown", "anki.flash.errors.load_notes_failed": "Failed to load flash notes", "anki.flash.errors.save_notes_failed": "Failed to save flash notes", "anki.flash.errors.update_note_index_out_of_bounds": "Failed to update note: index out of bounds", "anki.flash.example_note": "Example Note", "anki.flash.example_note_content": "This is an example note. You can click to edit or create new notes.\n\nFlash notes can be used to quickly record ideas and inspiration.", "anki.flash.example_note_preview": "This is an example note. You can click to edit or create new notes.", "anki.flash.export_failed": "Export failed: @error", "anki.flash.export_feature_under_development": "Export feature under development...", "anki.flash.messages.exported_to_anki": "Exported note to <PERSON><PERSON>", "anki.flash.messages.exported_to_markdown": "Exported note to Markdown", "anki.flash.messages.loaded_notes": "Loaded @count flash notes", "anki.flash.messages.saved_notes": "Saved @count flash notes", "anki.flash.messages.user_cancelled_export": "User cancelled export operation", "anki.flash.new_note": "New Note", "anki.flash.note_exported_to": "Note exported to: @path", "anki.flash.untitled_note": "Untitled Note", "anki.image_card.auto_ocr": "Auto OCR", "anki.image_card.cancel": "Cancel", "anki.image_card.cards_added_to_anki": "@count cards added to <PERSON>ki", "anki.image_card.click_or_drag_to_select": "Click to select image, or drag image here", "anki.image_card.click_to_select": "Click to select image", "anki.image_card.complete_and_return": "Complete and Return", "anki.image_card.config_title": "Configuration", "anki.image_card.deck_name_required": "Deck name cannot be empty", "anki.image_card.default_cloze_mode": "De<PERSON>ult <PERSON>", "anki.image_card.default_cloze_mode_placeholder": "Select default cloze mode", "anki.image_card.default_deck": "Default <PERSON>", "anki.image_card.default_deck_placeholder": "Enter default deck name", "anki.image_card.default_tags": "Default Tags", "anki.image_card.default_tags_placeholder": "Enter default tags, separated by commas", "anki.image_card.delete": "Delete", "anki.image_card.delete_all_confirmation": "Delete all images?", "anki.image_card.delete_all_masks": "Delete All Masks", "anki.image_card.delete_all_masks_confirmation": "Are you sure you want to delete all masks?", "anki.image_card.delete_all_masks_title": "Delete All Masks", "anki.image_card.delete_image_title": "Delete Image", "anki.image_card.failed": "Failed", "anki.image_card.failed_to_read_image_from_clipboard": "Failed to read image from clipboard", "anki.image_card.file_format_error": "Please select files in .png, .jpg, .jpeg, .gif, .bmp format", "anki.image_card.file_selection_failed": "File selection failed", "anki.image_card.free_guess": "Free Guess", "anki.image_card.generate_card": "", "anki.image_card.group_mode_title": "Group Mode", "anki.image_card.guru_import": "Guru <PERSON>", "anki.image_card.image_added_from_capture": "Screenshot captured and added", "anki.image_card.image_added_from_clipboard": "Image added from clipboard", "anki.image_card.image_cloze": "Image Occlusion", "anki.image_card.images_selected": "@count images selected", "anki.image_card.mask_all_guess_all": "Mask All Guess All", "anki.image_card.mask_all_guess_one": "Mask All Guess One", "anki.image_card.mask_one_guess_one": "Mask One Guess One", "anki.image_card.masks_deleted_count": "Deleted @count masks", "anki.image_card.masks_deleted_success": "Deletion successful", "anki.image_card.next_image": "Next Image", "anki.image_card.no_image_selected": "No image selected", "anki.image_card.no_masks_to_delete": "No masks to delete", "anki.image_card.not_set": "Not Set", "anki.image_card.note_label": "Note", "anki.image_card.note_placeholder": "Add note", "anki.image_card.one_cloze_per_card": "One Cloze Per Card", "anki.image_card.page_indicator": "Image @current/@total", "anki.image_card.paste_hotkey": "<PERSON>e <PERSON>", "anki.image_card.please_add_images_first": "Please add images first", "anki.image_card.previous_image": "Previous Image", "anki.image_card.primary_color": "Mask Color (Primary)", "anki.image_card.redo": "Redo", "anki.image_card.scratch_guess": "<PERSON><PERSON><PERSON>", "anki.image_card.secondary_color": "Mask Color (Secondary)", "anki.image_card.settings_saved": "Setting<PERSON> saved", "anki.image_card.snap_hotkey": "Screenshot Hotkey", "anki.image_card.submit": "Submit", "anki.image_card.success": "Success", "anki.image_card.tags_label": "Tags", "anki.image_card.tags_placeholder": "Enter tags, separated by commas", "anki.image_card.title": "Image Flashcards", "anki.image_card.undo": "Undo", "anki.llm_card.add_divider": "Add Divider", "anki.llm_card.add_image": "Add Image", "anki.llm_card.advanced_settings": "Advanced Settings", "anki.llm_card.ai_card_title": "AI Flashcards", "anki.llm_card.answer_cloze": "Answer Cloze", "anki.llm_card.api_address": "API Address", "anki.llm_card.api_key": "API Key", "anki.llm_card.api_key_cannot_empty": "API key cannot be empty", "anki.llm_card.at_least_one_card_type": "Select at least one card type", "anki.llm_card.at_least_one_difficulty": "Select at least one difficulty level", "anki.llm_card.auto_save_failed": "Auto-save failed: @error", "anki.llm_card.bold": "Bold", "anki.llm_card.cancel": "Cancel", "anki.llm_card.cannot_open_link": "Cannot open link: @url", "anki.llm_card.character_unit": "Characters", "anki.llm_card.choice_card": "Multiple Choice", "anki.llm_card.chunk_size": "Text Chunk Size", "anki.llm_card.chunk_size_cannot_empty": "Text chunk size cannot be empty", "anki.llm_card.chunk_size_range_error": "Text chunk size must be greater than 0", "anki.llm_card.cloze_card": "C<PERSON>ze Deletion", "anki.llm_card.code_block": "Code Block", "anki.llm_card.confirm": "Confirm", "anki.llm_card.confirm_delete": "Confirm Delete", "anki.llm_card.copy_success": "<PERSON><PERSON>d successfully", "anki.llm_card.copy_suffix": "Copy", "anki.llm_card.copy_suffix_numbered": "Copy @number", "anki.llm_card.custom": "Custom", "anki.llm_card.custom_model": "Custom Model", "anki.llm_card.custom_model_required": "When using custom model, please enter model name and API endpoint", "anki.llm_card.default_cloze_mode": "De<PERSON>ult <PERSON>", "anki.llm_card.delete": "Delete", "anki.llm_card.delete_prompt_confirmation": "Are you sure you want to delete prompt \"@name\"?", "anki.llm_card.delete_success": "Deleted successfully", "anki.llm_card.difficulty_advanced": "Advanced", "anki.llm_card.difficulty_basic": "Basic", "anki.llm_card.difficulty_medium": "Intermediate", "anki.llm_card.difficulty_none": "None", "anki.llm_card.error": "Error", "anki.llm_card.feature_description": "Supports calling AI large models to automatically generate cards", "anki.llm_card.free_guess": "Free Guess", "anki.llm_card.function_description": "Function Description", "anki.llm_card.generate_card_type": "Card Types to Generate", "anki.llm_card.generate_prompt": "Generate Prompt", "anki.llm_card.generate_prompt_failed": "Failed to generate prompt: @error", "anki.llm_card.get_prompt_from_storage": "Get prompt from storage: @name", "anki.llm_card.guru_import": "Guru <PERSON>", "anki.llm_card.guru_official": "Guru Official", "anki.llm_card.html_escape": "HTML Escape", "anki.llm_card.hyperlink": "Hyperlink", "anki.llm_card.image_url": "Image URL", "anki.llm_card.input_api_address_placeholder": "Enter API address URL, e.g.: https://example.com/v1", "anki.llm_card.input_api_key_placeholder": "Please enter API key", "anki.llm_card.input_chunk_size_placeholder": "Enter text chunk size", "anki.llm_card.input_custom_model_name": "Enter custom model name", "anki.llm_card.input_max_concurrent_requests_placeholder": "Enter maximum concurrent requests", "anki.llm_card.input_max_tokens_placeholder": "Enter maximum output tokens", "anki.llm_card.input_model": "Input Model", "anki.llm_card.input_page_range_placeholder": "Enter page range, leave blank for all", "anki.llm_card.input_prompt_title_placeholder": "Enter prompt title, e.g.: Q&A Card Prompt", "anki.llm_card.input_tags": "Enter Tags", "anki.llm_card.input_temperature_placeholder": "Enter temperature, between 0-2", "anki.llm_card.input_timeout_placeholder": "Enter timeout", "anki.llm_card.input_top_k_placeholder": "Enter Top K", "anki.llm_card.input_top_p_placeholder": "Enter Top P", "anki.llm_card.insert_image": "Insert Image", "anki.llm_card.insert_link": "Insert Link", "anki.llm_card.invalid_number": "Please enter a valid number", "anki.llm_card.italic": "Italic", "anki.llm_card.judge_card": "True/False", "anki.llm_card.log_directory": "Log Directory", "anki.llm_card.mask_all_guess_all": "Mask All Guess All", "anki.llm_card.mask_all_guess_one": "Mask All Guess One", "anki.llm_card.mask_one_guess_one": "Mask One Guess One", "anki.llm_card.max_concurrent_requests": "Maximum Concurrent Requests", "anki.llm_card.max_concurrent_requests_cannot_empty": "Maximum concurrent requests cannot be empty", "anki.llm_card.max_concurrent_requests_range_error": "Maximum concurrent requests must be greater than 0", "anki.llm_card.max_tokens": "Maximum Output Tokens", "anki.llm_card.model_provider": "Model Provider", "anki.llm_card.multi_choice_card": "Multiple Choice", "anki.llm_card.my_prompts": "My Prompts", "anki.llm_card.no_prompts_message": "No prompts available, please create one first", "anki.llm_card.one_cloze_per_card": "One Cloze Per Card", "anki.llm_card.open_link_error": "Error opening link: @error", "anki.llm_card.ordered_list": "Ordered List", "anki.llm_card.page_range": "Page Range", "anki.llm_card.parse_prompt_json_failed": "Failed to parse prompt JSON: @error", "anki.llm_card.prompt_copied": "Prompt copied", "anki.llm_card.prompt_deleted": "Prompt deleted", "anki.llm_card.prompt_management_tab": "Prompt Management", "anki.llm_card.prompt_name_cannot_empty": "Prompt name cannot be empty", "anki.llm_card.prompt_name_exists_renamed": "Prompt name \"@name\" already exists, automatically renamed to \"@uniqueName\"", "anki.llm_card.prompt_saved": "Prompt saved", "anki.llm_card.prompt_selection_changed": "Prompt selection changed, reloading prompt list: @value", "anki.llm_card.protocol_type": "Protocol Type", "anki.llm_card.qa_card": "Q&A", "anki.llm_card.question_difficulty": "Question Difficulty", "anki.llm_card.quote": "Quote", "anki.llm_card.reasoning_model": "Reasoning Model", "anki.llm_card.save": "Save", "anki.llm_card.select_at_least_one_card_type": "Please select at least one card type", "anki.llm_card.select_at_least_one_file": "Please select at least one file as card material", "anki.llm_card.select_local_image": "Select Local Image", "anki.llm_card.select_model": "Select Model", "anki.llm_card.select_model_provider": "Select Model Provider", "anki.llm_card.select_prompt": "", "anki.llm_card.select_protocol_type": "Select Protocol Type", "anki.llm_card.select_system_prompt": "Select System Prompt", "anki.llm_card.select_tags": "Select Tags", "anki.llm_card.smart_card_tab": "Smart Card Creation", "anki.llm_card.sort_by_create_time": "By Creation Time", "anki.llm_card.sort_by_modify_time": "By Modification Time", "anki.llm_card.sort_by_name": "By Name", "anki.llm_card.sort_by_word_count": "By Word Count", "anki.llm_card.strikethrough": "Strikethrough", "anki.llm_card.success": "Success", "anki.llm_card.system_default": "System Default", "anki.llm_card.system_prompt": "System Prompt", "anki.llm_card.temperature": "Temperature", "anki.llm_card.temperature_cannot_empty": "Temperature cannot be empty", "anki.llm_card.temperature_range_error": "Temperature must be between 0-2", "anki.llm_card.test": "Test", "anki.llm_card.timeout_cannot_empty": "Timeout cannot be empty", "anki.llm_card.timeout_seconds": "Timeout (seconds)", "anki.llm_card.title": "Title", "anki.llm_card.title_cannot_empty": "Title cannot be empty", "anki.llm_card.tokens_cannot_empty": "Token count cannot be empty", "anki.llm_card.top_k": "Top K", "anki.llm_card.top_k_cannot_empty": "Top K cannot be empty", "anki.llm_card.top_p": "Top P", "anki.llm_card.top_p_cannot_empty": "Top P cannot be empty", "anki.llm_card.top_p_range_error": "Top P must be between 0-1", "anki.llm_card.unable_to_load_content": "Unable to load original content, please re-edit.", "anki.llm_card.underline": "Underline", "anki.llm_card.unnamed_prompt": "Unnamed Prompt", "anki.llm_card.unordered_list": "Unordered List", "anki.llm_card.update_prompt_name_exists_renamed": "Updated prompt name \"@name\" already exists, automatically renamed to \"@uniqueName\"", "anki.llm_card.use_ai_tags": "", "anki.markdown_card.answer_cloze": "Answer Cloze", "anki.markdown_card.at_least_one_cloze_grammar": "Select at least one cloze grammar", "anki.markdown_card.chinese_character_set": "Chinese Character Set", "anki.markdown_card.cloze_grammar": "Cloze Grammar", "anki.markdown_card.cloze_tab": "C<PERSON>ze <PERSON>s", "anki.markdown_card.feature_description": "Supports creating flashcards from Markdown notes, supports Q&A and cloze cards", "anki.markdown_card.free_guess": "Free Guess", "anki.markdown_card.function_description": "Function Description", "anki.markdown_card.guru_import": "Guru <PERSON>", "anki.markdown_card.ignore_group": "Ignore Grouping", "anki.markdown_card.input_tags": "Enter Tags", "anki.markdown_card.mask_all_guess_all": "Mask All Guess All", "anki.markdown_card.mask_all_guess_one": "Mask All Guess One", "anki.markdown_card.mask_one_guess_one": "Mask One Guess One", "anki.markdown_card.media_file_directory": "Media File Directory", "anki.markdown_card.media_folder_placeholder": "Media folder, leave blank to use same directory as file", "anki.markdown_card.none": "None", "anki.markdown_card.obsidian_syntax": "Obsidian Syntax", "anki.markdown_card.one_cloze_per_card": "One Cloze Per Card", "anki.markdown_card.please_select_files": "Please select files!", "anki.markdown_card.please_select_kevin_text_cloze_template": "Please select [<PERSON> Cloze v3] template", "anki.markdown_card.please_set_front_field_pattern": "Please set Front field pattern!", "anki.markdown_card.processing_question": "Processing question @current", "anki.markdown_card.qa_tab": "Q&A Cards", "anki.markdown_card.select_tags": "Select Tags", "anki.markdown_card.separator": "Separator", "anki.markdown_card.title": "Markdown Flashcards", "anki.media_card.audio": "Audio", "anki.media_card.card_template": "Card Template", "anki.media_card.end_time": "End Time", "anki.media_card.end_time_error": "Please enter correct end time, e.g.: 00:00:00", "anki.media_card.end_time_placeholder": "Enter end time, e.g.: 00:00:00", "anki.media_card.feature_description": "Supports creating multimedia flashcards with text, images, audio, and video", "anki.media_card.ffmpeg_processing_failed": "FFmpeg processing failed: @error", "anki.media_card.field_mapping": "Field Mapping", "anki.media_card.file_content": "File Content", "anki.media_card.file_suffix": "File Extension", "anki.media_card.filename": "Filename", "anki.media_card.function_description": "Function Description", "anki.media_card.guru_import": "Guru <PERSON>", "anki.media_card.image": "Image", "anki.media_card.input_file_not_exist": "Input file does not exist: @path", "anki.media_card.input_file_suffix": "Enter file extension", "anki.media_card.input_tags": "Enter Tags", "anki.media_card.left_padding_error": "Please enter correct left padding, e.g.: 200", "anki.media_card.left_padding_ms": "Left Padding (ms)", "anki.media_card.left_padding_placeholder": "Enter left padding in milliseconds", "anki.media_card.match_type": "Match Type", "anki.media_card.media_card_title": "Multimedia Flashcards", "anki.media_card.media_directory_not_exist": "Media directory does not exist", "anki.media_card.media_file": "Media File", "anki.media_card.media_file_directory": "Media File Directory", "anki.media_card.media_file_directory_placeholder": "Please enter media file directory", "anki.media_card.no_matching_files_found": "No files found with matching extension", "anki.media_card.no_valid_cards_generated": "No valid cards generated", "anki.media_card.none": "None", "anki.media_card.operation_failed": "Operation failed: @error", "anki.media_card.please_configure_ffmpeg_path": "Please configure FFmpeg path in preferences", "anki.media_card.please_select_file_suffix": "Please select file extension", "anki.media_card.please_select_media_directory": "Please select media file directory", "anki.media_card.please_select_subtitle_and_media_files": "Please select subtitle and media files", "anki.media_card.processing": "Processing...", "anki.media_card.processing_file": "Processing file: @filename", "anki.media_card.qa_card_tab": "Q&A Cards", "anki.media_card.right_padding_error": "Please enter correct right padding, e.g.: 200", "anki.media_card.right_padding_ms": "Right Padding (ms)", "anki.media_card.right_padding_placeholder": "Enter right padding in milliseconds", "anki.media_card.select_card_template": "Select Card Template", "anki.media_card.select_file_suffix": "Select File Extension", "anki.media_card.select_match_type": "Select Match Type", "anki.media_card.select_tags": "Select Tags", "anki.media_card.start_time": "Start Time", "anki.media_card.start_time_error": "Please enter correct start time, e.g.: 00:00:00", "anki.media_card.start_time_placeholder": "Enter start time, e.g.: 00:00:00", "anki.media_card.subtitle_card_tab": "Subtitle Cards", "anki.media_card.subtitle_file": "Subtitle File", "anki.media_card.subtitle_offset_error": "Please enter correct subtitle offset, e.g.: 200", "anki.media_card.subtitle_offset_ms": "Subtitle Offset (ms)", "anki.media_card.subtitle_offset_placeholder": "Enter subtitle offset in milliseconds", "anki.media_card.template_field": "Template Field", "anki.media_card.text": "Text", "anki.media_card.video": "Video", "anki.media_card.video_output_format": "Video Output Format", "anki.mindmap.at_least_one_cloze_style": "Select at least one cloze style", "anki.mindmap.blue_font": "Blue Text", "anki.mindmap.blue_highlight": "Blue Highlight", "anki.mindmap.blue_underline": "Blue Underline", "anki.mindmap.cloze_mode": "Cloze Mode", "anki.mindmap.cloze_style": "Cloze Style", "anki.mindmap.feature_description": "Supports creating cards from Xmind, Zhixi, Mubu, Markdown and other sources, unlimited levels, supports cloze and Q&A cards", "anki.mindmap.green_font": "Green Text", "anki.mindmap.green_highlight": "Green Highlight", "anki.mindmap.green_underline": "Green Underline", "anki.mindmap.highlight_color": "Highlight Color", "anki.mindmap.input_color_placeholder": "Enter color, e.g. #FF0000", "anki.mindmap.input_file_required": "Input file cannot be empty!", "anki.mindmap.map_id_placeholder": "Enter MapID, used for card updates", "anki.mindmap.media_file_directory": "Media File Directory", "anki.mindmap.media_folder_placeholder": "Media folder, leave blank to use same directory as file", "anki.mindmap.mindmap_card_title": "Mind Map Flashcards", "anki.mindmap.mindmap_source": "Mind Map Source", "anki.mindmap.obsidian_syntax": "Obsidian Syntax", "anki.mindmap.red_font": "Red Text", "anki.mindmap.red_highlight": "Red Highlight", "anki.mindmap.red_underline": "Red Underline", "anki.mindmap.select_color": "Select or Enter Color", "anki.mindmap.source_mubu": "<PERSON><PERSON>", "anki.mindmap.source_zhixi": "Zhixi", "anki.mindmap.strikeout": "Strikethrough", "anki.mindmap.text_color": "Text Color", "anki.mindmap.text_highlight_short": "Text Highlight", "anki.mindmap.yellow_highlight": "Yellow Highlight", "anki.mubu.answer_cloze": "Answer Cloze", "anki.mubu.back": "Back (System Built-in)", "anki.mubu.blue": "Blue", "anki.mubu.bold": "Bold", "anki.mubu.cannot_extract_token": "Unable to extract jwt-token from <PERSON><PERSON>, please check if the Cookie is valid", "anki.mubu.card_generation_complete": "Card generation complete", "anki.mubu.card_template": "Card Template", "anki.mubu.card_type": "Card Type", "anki.mubu.cloze": "<PERSON><PERSON><PERSON>", "anki.mubu.cloze_mode": "Cloze Mode", "anki.mubu.cloze_style": "Cloze Style", "anki.mubu.connection_timeout": "Connection timeout, please check your network", "anki.mubu.cookie": "<PERSON><PERSON>", "anki.mubu.cookie_cannot_be_empty": "<PERSON><PERSON> cannot be empty", "anki.mubu.cookie_validation_error": "Cookie validation error occurred", "anki.mubu.cookie_validation_failed": "<PERSON><PERSON> validation failed", "anki.mubu.create_time": "Creation Time", "anki.mubu.cyan": "<PERSON><PERSON>", "anki.mubu.document_data_empty": "Document data is empty, unable to parse structure", "anki.mubu.document_definition_empty": "Document definition is empty, unable to parse structure", "anki.mubu.document_id": "Document ID", "anki.mubu.document_no_content": "Document has no content or parsing failed", "anki.mubu.export_failed": "Export failed", "anki.mubu.feature_description": "Supports creating flashcards from Mubu notes, supports Q&A and cloze cards, also supports batch export of Mubu notes", "anki.mubu.free_guess": "Free Guess", "anki.mubu.front": "Front (System Built-in)", "anki.mubu.front_field_mapping_error": "Please set one field as front content (Front)", "anki.mubu.function_description_title": "Function Description", "anki.mubu.green": "Green", "anki.mubu.grey": "Grey", "anki.mubu.height": "Height", "anki.mubu.hierarchy": "<PERSON><PERSON><PERSON><PERSON>", "anki.mubu.importing_cards": "Importing cards...", "anki.mubu.input_cookie": "<PERSON><PERSON>", "anki.mubu.input_document": "Enter Document", "anki.mubu.input_tags": "Enter Tags", "anki.mubu.italic": "Italic", "anki.mubu.item_count": "<PERSON><PERSON>", "anki.mubu.level_1": "Level 1 Heading", "anki.mubu.level_2": "Level 2 Heading", "anki.mubu.level_3": "Level 3 Heading", "anki.mubu.level_4": "Level 4 Heading", "anki.mubu.level_5": "Level 5 Heading", "anki.mubu.level_6": "Level 6 Heading", "anki.mubu.mask_all_guess_all": "Mask All Guess All", "anki.mubu.mask_all_guess_one": "Mask All Guess One", "anki.mubu.mask_one_guess_one": "Mask One Guess One", "anki.mubu.mindmap": "Complete Mind Map", "anki.mubu.network_error": "Network Error", "anki.mubu.no_auth_info": "Authentication information not set, please call set<PERSON><PERSON><PERSON>() first", "anki.mubu.node_desc": "Node-Description", "anki.mubu.none": "None", "anki.mubu.olive": "<PERSON>", "anki.mubu.one_cloze_per_card": "One Cloze Per Card", "anki.mubu.page_title": "Mubu Flashcards", "anki.mubu.parse_document_definition_json_failed": "Failed to parse document definition JSON", "anki.mubu.part_card": "Partial Card Creation", "anki.mubu.pink": "Pink", "anki.mubu.processing_node": "Processing node", "anki.mubu.purple": "Purple", "anki.mubu.q_node_level": "Question Node Level", "anki.mubu.qa": "Q&A", "anki.mubu.receive_timeout": "Receive response timeout, please check your network", "anki.mubu.red": "Red", "anki.mubu.request_canceled": "Request canceled", "anki.mubu.select_card_template": "Select Card Template", "anki.mubu.select_cloze_style": "Please select at least one cloze style", "anki.mubu.select_q_node_level": "Select Question Node Level", "anki.mubu.select_tags": "Select Tags", "anki.mubu.select_target_document": "Select Target Document", "anki.mubu.select_text_color": "Select Color", "anki.mubu.select_text_highlight": "Select Color", "anki.mubu.send_timeout": "Send request timeout, please check your network", "anki.mubu.sep": "---", "anki.mubu.server_error": "Server returned an error", "anki.mubu.show_source": "Show Source", "anki.mubu.strikeout": "Strikethrough", "anki.mubu.tab_card_creation": "Note to Cards", "anki.mubu.tab_note_export": "Note Export", "anki.mubu.target_document": "Target Document", "anki.mubu.text_color": "Text Color", "anki.mubu.text_highlight": "Highlight Color", "anki.mubu.underline": "Underline", "anki.mubu.unknow_error": "Unknown error occurred", "anki.mubu.update_time": "Update Time", "anki.mubu.view_source": "View Source", "anki.mubu.width": "<PERSON><PERSON><PERSON>", "anki.mubu.yellow": "Yellow", "anki.ocr.all_text": "All Text", "anki.ocr.and_other_errors": "and @count other errors", "anki.ocr.cancel": "Cancel", "anki.ocr.cannot_convert_image_to_png": "Cannot convert image to PNG format", "anki.ocr.cannot_process_any_ocr_results": "Cannot process any OCR results", "anki.ocr.cannot_save_any_valid_image_files": "Cannot save any valid image files", "anki.ocr.card_id": "Card ID", "anki.ocr.card_id_cannot_empty": "Card ID cannot be empty", "anki.ocr.card_id_illegal": "Card ID does not exist or is invalid, example: 1736773785607,1736773785608", "anki.ocr.card_id_placeholder": "Enter card ID list, separated by commas, e.g.: 1736080247115,1736080247119", "anki.ocr.card_ocr_description": "Perform OCR on images in Anki cards and update field content", "anki.ocr.card_ocr_tab": "Card OCR", "anki.ocr.card_ocr_title": "Card OCR", "anki.ocr.card_template": "Card Template", "anki.ocr.clickToSelectImage": "Click to select image", "anki.ocr.clickToSelectOrDragImage": "Click to select image, or drag image here", "anki.ocr.clipboard_read_failed": "Failed to read image from clipboard", "anki.ocr.copied": "<PERSON>pied", "anki.ocr.copied_to_clipboard": "Copied to clipboard", "anki.ocr.copy": "Copy", "anki.ocr.custom": "Custom", "anki.ocr.defaultOcrProvider": "Default OCR Provider", "anki.ocr.delete": "Delete", "anki.ocr.deleteAllImages": "Delete all images?", "anki.ocr.deleteImage": "Delete Image", "anki.ocr.error_processing_single_image": "Error processing single image: @error", "anki.ocr.error_saving_image_files": "Error saving image files: @error", "anki.ocr.export_failed": "Export failed: @error", "anki.ocr.exported_to": "Exported to: @path", "anki.ocr.failed_to_recognize_any_images": "Failed to recognize any images", "anki.ocr.feature_description": "Perform OCR text recognition on images or images in Anki cards", "anki.ocr.feature_not_supported_on_current_system": "This feature is not supported on the current system", "anki.ocr.field_config": "Field Configuration", "anki.ocr.file_selection_failed": "File selection failed", "anki.ocr.fill_field": "Fill Field", "anki.ocr.imageDeleted": "Image has been deleted or does not exist", "anki.ocr.imageOcr": "Image OCR", "anki.ocr.image_data_invalid": "Image data invalid: @path", "anki.ocr.image_file_not_exist": "Image file does not exist: @path", "anki.ocr.image_ocr_tab": "Image OCR", "anki.ocr.merge_output": "Merge Output", "anki.ocr.noImageSelected": "No image selected", "anki.ocr.no_valid_image_in_clipboard": "No valid image in clipboard", "anki.ocr.ocr_engine_initialization_failed": "OCR engine initialization failed, please try restarting the app or updating to the latest version", "anki.ocr.ocr_model_file_invalid": "OCR model file invalid: @fileName", "anki.ocr.ocr_processing_failed": "OCR processing failed: @error", "anki.ocr.ocr_recognition_completed": "OCR recognition completed", "anki.ocr.ocr_recognition_failed": "OCR recognition failed: @error", "anki.ocr.ocr_recognition_partially_completed": "OCR recognition partially completed: @success/@total images processed", "anki.ocr.ocr_service_returned_empty_result": "OCR service returned empty result", "anki.ocr.original_field": "Original Field", "anki.ocr.pageIndicator": "Page @current/@total", "anki.ocr.partial_ocr_recognition_failed": "Some images failed OCR recognition, processing remaining images", "anki.ocr.pleaseSelectValidImageFormat": "Please select files in .png, .jpg, .jpeg format", "anki.ocr.please_add_images_first": "Please add images first", "anki.ocr.please_select_cards_first": "Please select cards first", "anki.ocr.processing_note": "Processing note: @noteId", "anki.ocr.recognizing": "Recognizing...", "anki.ocr.save_ocr_text": "Save OCR Text", "anki.ocr.selectOcrProvider": "Select OCR Provider", "anki.ocr.select_card_template": "Select Card Template", "anki.ocr.select_field": "Select Field", "anki.ocr.selectedImages": "Selected @count images", "anki.ocr.selected_text": "Selected Text", "anki.ocr.settings": "Settings", "anki.ocr.submit": "Submit", "anki.ocr.table_ocr_col": "OCR Recognition", "anki.ocr.table_replace_col": "Replace Content", "anki.ocr.text_copied_to_clipboard": "Text copied to clipboard", "anki.ocr.title": "OCR Recognition", "anki.pdf_card.a_item_id": "Answer item ID", "anki.pdf_card.a_page_range": "Answer file page range", "anki.pdf_card.annot_type_note": "Note", "anki.pdf_card.annot_type_text": "Text Box", "anki.pdf_card.annotation_types": "Annotation types", "anki.pdf_card.cloze_mode_free_guess": "Free Guess", "anki.pdf_card.cloze_mode_mask_all_guess_all": "Mask All Guess All", "anki.pdf_card.cloze_mode_mask_all_guess_one": "Mask All Guess One", "anki.pdf_card.cloze_mode_mask_one_guess_one": "Mask One Guess One", "anki.pdf_card.cloze_mode_scratch_guess": "<PERSON><PERSON><PERSON>", "anki.pdf_card.cloze_tab": "C<PERSON>ze <PERSON>s", "anki.pdf_card.exporting_item": "Exporting item...", "anki.pdf_card.extra_info": "Extra information", "anki.pdf_card.feature_description": "Supports creating cloze and Q&A cards with multiple card creation modes", "anki.pdf_card.full_page_cloze": "Full Page <PERSON>", "anki.pdf_card.function_description": "Function Description", "anki.pdf_card.getting_item_info": "Getting item info...", "anki.pdf_card.guru_import": "Guru <PERSON>", "anki.pdf_card.image_occlusion_builtin": "Image Occlusion (<PERSON><PERSON> Built-in)", "anki.pdf_card.invalid_answer_item_id": "Invalid answer item ID: @itemId", "anki.pdf_card.invalid_item_id": "Invalid item ID: @itemId", "anki.pdf_card.invalid_question_item_id": "Invalid question item ID: @itemId", "anki.pdf_card.main_color": "Primary Color", "anki.pdf_card.mask_color": "Mask Color", "anki.pdf_card.mask_type": "Mask type", "anki.pdf_card.mask_type_highlight": "Highlight", "anki.pdf_card.mask_type_square": "Rectangle", "anki.pdf_card.mask_type_squiggly": "Squiggly Line", "anki.pdf_card.mask_type_strikeout": "Strikethrough", "anki.pdf_card.mask_type_underline": "Underline", "anki.pdf_card.mix_card": "Mixed card creation", "anki.pdf_card.one_cloze_per_card": "One Cloze Per Card", "anki.pdf_card.pdf_columns": "PDF columns", "anki.pdf_card.please_select_file": "Please select file", "anki.pdf_card.q_item_id": "Question item ID", "anki.pdf_card.q_page_range": "Question file", "anki.pdf_card.qa_mode_dual_file": "Separate Q&A Files", "anki.pdf_card.qa_mode_dual_page": "Dual Page Cards", "anki.pdf_card.qa_mode_note": "Note Cards", "anki.pdf_card.qa_mode_single_file": "Continuous Q&A", "anki.pdf_card.qa_mode_single_page": "Single Page Cards", "anki.pdf_card.qa_tab": "Q&A Cards", "anki.pdf_card.second_color": "Secondary Color", "anki.pdf_card.title": "PDF Flashcards", "anki.pdf_card.zotero_card": "Zotero card creation", "anki.pdf_card.zotero_item_id": "Item ID", "anki.pdf_card.zotero_windows_mac_only": "Zotero card creation is only supported on Windows and Mac systems", "anki.pdf_note.actions.failed": "Failed", "anki.pdf_note.actions.hint": "Hint", "anki.pdf_note.actions.in_development": "In Development", "anki.pdf_note.actions.save": "Save", "anki.pdf_note.actions.success": "Success", "anki.pdf_note.config.auto_paste": "Auto Paste", "anki.pdf_note.config.custom_link_format": "Custom Link Format", "anki.pdf_note.config.custom_link_format_placeholder": "Enter custom link format, e.g.: [{{title}}](<{{url}}>)", "anki.pdf_note.config.link_format": "Link Format", "anki.pdf_note.config.link_protocol": "Jump Protocol", "anki.pdf_note.content.image": "Image", "anki.pdf_note.content.view_original": "[View Original]", "anki.pdf_note.description.function_title": "Function Description", "anki.pdf_note.description.main": "Take notes while reading PDFs, supports quick insertion of PDF page backlinks that can directly jump to the original source, compatible with Obsidian, Logseq, Feishu, Yuque, Notion, Word, OneNote and many other note-taking software", "anki.pdf_note.description.zotero_notice": "Note: This feature requires <PERSON><PERSON><PERSON> to work", "anki.pdf_note.errors.custom_link_format_empty": "Custom link format cannot be empty", "anki.pdf_note.errors.get_item_notes_failed": "Failed to get item notes", "anki.pdf_note.errors.get_pdf_info_failed": "Failed to get PDF info", "anki.pdf_note.errors.get_selected_annotation_failed": "Failed to get selected annotation", "anki.pdf_note.errors.image_data_empty": "Image data is empty", "anki.pdf_note.errors.no_bookmarks_in_pdf": "No bookmarks in PDF", "anki.pdf_note.errors.no_selected_annotation": "No selected annotation", "anki.pdf_note.errors.pdf_path_empty": "PDF path is empty", "anki.pdf_note.errors.text_data_empty": "Text data is empty", "anki.pdf_note.errors.unsupported_link_format": "Unsupported link format", "anki.pdf_note.formats.custom": "Custom", "anki.pdf_note.formats.html": "HTML", "anki.pdf_note.formats.markdown": "<PERSON><PERSON>", "anki.pdf_note.formats.url": "Url", "anki.pdf_note.notifications.all_notes_copied_success": "All notes copied successfully", "anki.pdf_note.notifications.all_notes_copy_failed": "Failed to insert all notes", "anki.pdf_note.notifications.annotation_copied_success": "Annotation copied successfully", "anki.pdf_note.notifications.annotation_copy_failed": "Failed to insert annotation", "anki.pdf_note.notifications.bookmark_links_copied_success": "Bookmark links copied successfully", "anki.pdf_note.notifications.bookmark_links_copy_failed": "Failed to insert bookmark links", "anki.pdf_note.notifications.flag_a_set_failed": "Failed to set flag A", "anki.pdf_note.notifications.flag_a_set_success": "Flag A set successfully", "anki.pdf_note.notifications.flag_b_set_failed": "Failed to set flag B", "anki.pdf_note.notifications.flag_b_set_success": "Flag B set successfully", "anki.pdf_note.notifications.flag_cleared_success": "Flags cleared successfully", "anki.pdf_note.notifications.flag_jump_failed": "Failed to jump to flag", "anki.pdf_note.notifications.go_end_failed": "Failed to go to last page", "anki.pdf_note.notifications.go_home_failed": "Failed to go to first page", "anki.pdf_note.notifications.go_next_failed": "Failed to go to next page", "anki.pdf_note.notifications.go_prev_failed": "Failed to go to previous page", "anki.pdf_note.notifications.link_copied_success": "Link copied successfully", "anki.pdf_note.notifications.link_copy_failed": "Failed to insert link", "anki.pdf_note.notifications.note_copied_success": "Note copied successfully", "anki.pdf_note.notifications.note_copy_failed": "Failed to insert note", "anki.pdf_note.notifications.ocr_failed": "OCR failed", "anki.pdf_note.notifications.ocr_in_development": "OCR feature in development", "anki.pdf_note.notifications.page_screenshot_failed": "Failed to insert page screenshot", "anki.pdf_note.notifications.page_screenshot_in_development": "Page screenshot feature in development", "anki.pdf_note.notifications.page_text_extract_failed": "Failed to extract page text", "anki.pdf_note.notifications.page_text_extract_in_development": "Page text extraction feature in development", "anki.pdf_note.notifications.please_set_flag_first": "Please set a flag first", "anki.pdf_note.notifications.scroll_down_failed": "Failed to scroll down", "anki.pdf_note.notifications.scroll_up_failed": "Failed to scroll up", "anki.pdf_note.notifications.settings_saved_success": "Setting<PERSON> saved successfully", "anki.pdf_note.notifications.shortcuts_disabled": "All shortcuts disabled", "anki.pdf_note.notifications.shortcuts_enabled": "All shortcuts enabled", "anki.pdf_note.shortcuts.clear_flag": "Clear Flags", "anki.pdf_note.shortcuts.disable_all": "Disable All", "anki.pdf_note.shortcuts.go_end": "Go to Last Page", "anki.pdf_note.shortcuts.go_home": "Go to First Page", "anki.pdf_note.shortcuts.go_next": "Next Page", "anki.pdf_note.shortcuts.go_prev": "Previous Page", "anki.pdf_note.shortcuts.insert_all_notes": "Insert All Notes", "anki.pdf_note.shortcuts.insert_bookmarks_link": "Insert Bookmark Links", "anki.pdf_note.shortcuts.insert_comment": "Insert Annotation", "anki.pdf_note.shortcuts.insert_comment_link": "Insert Annotation Link", "anki.pdf_note.shortcuts.insert_note": "Insert Note", "anki.pdf_note.shortcuts.insert_page_link": "Insert Page Link", "anki.pdf_note.shortcuts.insert_path_link": "Insert Path Link", "anki.pdf_note.shortcuts.jump_to_flag": "Jump to Flag", "anki.pdf_note.shortcuts.not_set": "Not Set", "anki.pdf_note.shortcuts.set_flag_a": "Set Flag A", "anki.pdf_note.shortcuts.set_flag_b": "Set Flag B", "anki.pdf_note.tabs.config": "Config", "anki.pdf_note.tabs.shortcuts": "Shortcuts", "anki.pdf_note.title": "PDF Notes", "anki.placeholder.a_item_id": "Enter answer item ID", "anki.placeholder.a_item_id_required": "Answer item ID cannot be empty", "anki.placeholder.a_page_range": "Answer file page range", "anki.placeholder.atLeastOneAnnotationType": "Select at least one annotation type", "anki.placeholder.atLeastOneAnswerClozeGrammar": "Select at least one cloze grammar", "anki.placeholder.atLeastOneMaskType": "Select at least one mask type", "anki.placeholder.extra_info": "Enter extra information, e.g., \"Book Title\"", "anki.placeholder.input_item_id": "Enter item ID", "anki.placeholder.input_tags": "Enter tags", "anki.placeholder.item_id_required": "Item ID cannot be empty", "anki.placeholder.mustBeGreaterThan0": "Must be greater than 0", "anki.placeholder.mustBeInteger": "Please enter an integer", "anki.placeholder.page_range": "Enter page range; leave blank for all", "anki.placeholder.page_range_required": "Enter a valid page range, e.g., 1-3,5-7", "anki.placeholder.pdf_columns": "Enter PDF column count", "anki.placeholder.pdf_columns_required": "PDF column count cannot be empty", "anki.placeholder.q_item_id": "Enter question item ID", "anki.placeholder.q_item_id_required": "Question item ID cannot be empty", "anki.placeholder.q_page_range": "Question file page range", "anki.placeholder.select_a_file": "Select answer file", "anki.placeholder.select_file": "Select files to compress", "anki.placeholder.select_q_file": "Select question file", "anki.placeholder.select_tags": "Select tags", "anki.placeholder.tags": "Select or enter tags", "anki.placeholder.target_deck_search_input": "Enter deck name", "anki.placeholder.zotero_item_id": "Enter item ID", "anki.placeholder.zotero_item_id_required": "Item ID cannot be empty", "anki.sep": "Separator", "anki.sync.config.dataLocation": "Data Storage Location", "anki.sync.config.dataLocationPlaceholder": "Select data storage location", "anki.sync.config.host": "Listen Address", "anki.sync.config.hostInvalid": "Please enter a valid IP address or domain name", "anki.sync.config.hostPlaceholder": "Enter listen address", "anki.sync.config.hostRequired": "Listen address cannot be empty", "anki.sync.config.max_payloa_invalid": "Please enter a positive integer, e.g., 100", "anki.sync.config.max_payloa_required": "Maximum sync payload cannot be empty", "anki.sync.config.max_payload": "Maximum Sync Payload (MB)", "anki.sync.config.max_payload_placeholder": "Enter maximum sync payload, default is 100", "anki.sync.config.password": "Password", "anki.sync.config.passwordMinLength": "Password must be at least 6 characters", "anki.sync.config.passwordPlaceholder": "Enter password", "anki.sync.config.passwordRequired": "Password cannot be empty", "anki.sync.config.port": "Listen Port", "anki.sync.config.portInvalid": "Please enter a port number between 1-65535", "anki.sync.config.portPlaceholder": "Enter listen port", "anki.sync.config.portRequired": "Listen port cannot be empty", "anki.sync.config.title": "Sync Configuration", "anki.sync.config.username": "Username", "anki.sync.config.usernamePlaceholder": "Enter username", "anki.sync.config.usernameRequired": "Username cannot be empty", "anki.sync.configIncomplete": "Please complete all configurations", "anki.sync.copied": "Copied: @address", "anki.sync.logStartServerError": "Start server error", "anki.sync.logStopServerError": "Stop server error", "anki.sync.needNotificationPermission": "Notification permission is required to start the sync server", "anki.sync.no_user": "No users configured", "anki.sync.serverAddress": "Server Address:", "anki.sync.serverStarted": "Sync server started!", "anki.sync.serverStopped": "Sync server stopped", "anki.sync.setDataLocation": "Please set data storage location", "anki.sync.settingsSaved": "Setting<PERSON> saved", "anki.sync.startServerError": "Start server error: {error}", "anki.sync.startServerFailed": "Failed to start sync server: {message}", "anki.sync.startingServer": "Starting server...", "anki.sync.stopServerError": "Stop server error", "anki.sync.stopServerFailed": "Failed to stop sync server", "anki.sync.syncServer": "Sync Server", "anki.sync.title": "<PERSON><PERSON>", "anki.sync.user.add": "Add", "anki.sync.user.addError": "Failed to add user", "anki.sync.user.addFirst": "Add User", "anki.sync.user.addSuccess": "User added successfully", "anki.sync.user.addTitle": "Add New User", "anki.sync.user.cancel": "Cancel", "anki.sync.user.delete": "Delete", "anki.sync.user.deleteConfirm": "Are you sure you want to delete user '@username'?", "anki.sync.user.deleteError": "Failed to delete user", "anki.sync.user.deleteSuccess": "User deleted successfully", "anki.sync.user.deleteTitle": "Delete User", "anki.sync.user.edit": "Edit", "anki.sync.user.editTitle": "Edit User", "anki.sync.user.noUsers": "No users configured", "anki.sync.user.notFound": "User not found", "anki.sync.user.title": "User Management", "anki.sync.user.update": "Update", "anki.sync.user.updateError": "Failed to update user", "anki.sync.user.updateSuccess": "User updated successfully", "anki.sync.user.usernameExists": "Username already exists", "anki.text": "Text", "anki.text_cannot_empty": "Text cannot be empty", "anki.text_card.answer_after_option": "Answer after options", "anki.text_card.answer_cloze": "Answer Cloze", "anki.text_card.answer_file_fields": "Answer file contains fields", "anki.text_card.answer_in_question": "Answer in question", "anki.text_card.at_least_one_cloze_grammar": "Select at least one cloze grammar", "anki.text_card.card_template": "Card Template", "anki.text_card.choice_tab": "Multiple Choice", "anki.text_card.cloze_grammar": "Cloze Grammar", "anki.text_card.cloze_tab": "C<PERSON>ze <PERSON>s", "anki.text_card.document_type": "Document Type", "anki.text_card.feature_description": "Supports creating flashcards from TXT, Markdown and other formats, supports cloze, Q&A, multiple choice, and true/false cards with multiple creation modes", "anki.text_card.file_path_not_exist": "File path does not exist", "anki.text_card.fixed_options_true_false": "True||False", "anki.text_card.free_guess": "Free Guess", "anki.text_card.function_description": "Function Description", "anki.text_card.ignore_group": "Ignore Grouping", "anki.text_card.input_subdeck_prefix": "Enter subdeck prefix", "anki.text_card.input_tags": "Enter Tags", "anki.text_card.judge_tab": "True/False", "anki.text_card.mask_all_guess_all": "Mask All Guess All", "anki.text_card.mask_all_guess_one": "Mask All Guess One", "anki.text_card.mask_one_guess_one": "Mask One Guess One", "anki.text_card.media_file_directory": "Media File Directory", "anki.text_card.media_folder_placeholder": "Media folder, leave blank to use same directory as file", "anki.text_card.none": "None", "anki.text_card.normal_separator": "Normal Separator", "anki.text_card.obsidian_syntax": "Obsidian Syntax", "anki.text_card.one_cloze_per_card": "One Cloze Per Card", "anki.text_card.please_select_answer_file": "Please select answer file", "anki.text_card.please_select_file": "Please select file", "anki.text_card.please_select_file_not_directory": "Please select file, not directory", "anki.text_card.please_select_files": "Please select files!", "anki.text_card.please_select_kevin_choice_template": "Please select [Kevin Choice Card v2] template", "anki.text_card.please_select_kevin_text_cloze_template": "Please select [<PERSON> Cloze v3] template", "anki.text_card.please_select_question_answer_files": "Please select question and answer files!", "anki.text_card.please_select_question_file": "Please select question file", "anki.text_card.please_set_answers_field_pattern": "Please set Answers field pattern!", "anki.text_card.please_set_front_field_pattern": "Please set Front field pattern!", "anki.text_card.please_set_question_field_pattern": "Please set Question field pattern!", "anki.text_card.processing_question": "Processing question @current", "anki.text_card.qa_tab": "Q&A", "anki.text_card.question_answer_count_mismatch": "Question count (@questionCount) and answer count (@answerCount) mismatch in deck @deck!", "anki.text_card.question_answer_cross_file": "Questions and answers in separate files", "anki.text_card.question_answer_cross_file_choice": "Questions and answers in separate files", "anki.text_card.question_answer_same_file": "Questions and answers in same file", "anki.text_card.question_file_fields": "Question file contains fields", "anki.text_card.question_single_line": "Single line question", "anki.text_card.regex_separator": "Regex Separator", "anki.text_card.restore_params": "Restore Parameters", "anki.text_card.save_params": "Save Parameters", "anki.text_card.select_answer_file_fields": "Select answer file fields", "anki.text_card.select_answer_file_fields_required": "Please select answer file fields", "anki.text_card.select_answer_file_placeholder": "Please select answer file", "anki.text_card.select_card_template": "Select Card Template", "anki.text_card.select_question_file_fields": "Select question file fields", "anki.text_card.select_question_file_fields_required": "Please select question file fields", "anki.text_card.select_subdeck_prefix": "Select Subdeck Prefix", "anki.text_card.select_tags": "Select Tags", "anki.text_card.subdeck_prefix": "Subdeck Prefix", "anki.text_card.title": "Text Flashcards", "anki.tts.add_voice": "Add Voice", "anki.tts.card_id": "Card ID", "anki.tts.card_id_cannot_empty": "Card ID cannot be empty", "anki.tts.card_id_illegal": "Card ID does not exist or is invalid, example: 1736773785607,1736773785608", "anki.tts.card_id_placeholder": "Enter card ID list, separated by commas, e.g.: 1736080247115,1736080247119", "anki.tts.card_template": "Card Template", "anki.tts.card_tts_tab": "Card TTS", "anki.tts.card_tts_title": "Card Text-to-Speech", "anki.tts.failed": "Failed", "anki.tts.feature_description": "Supports batch adding voice to <PERSON>ki cards, supports multiple TTS engines and text-to-speech", "anki.tts.feature_not_supported_on_current_system": "This feature is not supported on the current system", "anki.tts.fill_field": "Fill Field", "anki.tts.function_description": "Function Description", "anki.tts.input_sep": "Enter separator", "anki.tts.input_text": "Enter text...", "anki.tts.is_use_sep": "Enable separator", "anki.tts.lang.chinese": "Chinese", "anki.tts.lang.english_uk": "English (UK)", "anki.tts.lang.english_us": "English (US)", "anki.tts.lang.french": "French", "anki.tts.lang.german": "German", "anki.tts.lang.italian": "Italian", "anki.tts.lang.japanese": "Japanese", "anki.tts.lang.korean": "Korean", "anki.tts.lang.portuguese": "Portuguese", "anki.tts.lang.russian": "Russian", "anki.tts.lang.spanish": "Spanish", "anki.tts.language": "Language", "anki.tts.original_field": "Original Field", "anki.tts.output_directory_cannot_be_empty": "Output directory cannot be empty", "anki.tts.pitch": "Pitch", "anki.tts.please_select_cards_first": "Please select cards first", "anki.tts.processing": "Processing...", "anki.tts.processing_note": "Processing note: @noteId", "anki.tts.rate": "Speech Rate", "anki.tts.remove_voice": "Remove Voice", "anki.tts.select_card_template": "Select Card Template", "anki.tts.select_field": "Select Field", "anki.tts.select_language": "Select Language", "anki.tts.select_sep": "Select Separator", "anki.tts.select_style": "Select Style", "anki.tts.style": "Style", "anki.tts.text_cannot_be_empty": "Text cannot be empty", "anki.tts.text_tts_tab": "Text-to-Speech", "anki.tts.voice.cantonese_hongkong": "Cantonese-Hong Kong", "anki.tts.voice.female": "Female", "anki.tts.voice.male": "Male", "anki.tts.voice.multilingual": "Multilingual", "anki.tts.voice.northeast_dialect": "Northeast Dialect", "anki.tts.voice.shaanxi_dialect": "Shaanxi Dialect", "anki.tts.voice_field_config": "Voice Field Configuration", "anki.tts.voice_generation_failed": "Voice generation failed: @message", "anki.tts.voice_settings": "Voice Settings", "anki.tts.volume": "Volume", "anki.wereader_card.added_highlight_at": "Added highlight at @time", "anki.wereader_card.answer_cloze": "Answer Cloze", "anki.wereader_card.api_error": "Error: @message", "anki.wereader_card.api_request": "Request: @uri", "anki.wereader_card.api_request_failed": "Request failed: @statusCode", "anki.wereader_card.ascending_order": "Ascending", "anki.wereader_card.at_least_one_export_type": "Select at least one export type", "anki.wereader_card.at_least_one_note_type": "Select at least one note type", "anki.wereader_card.cannot_get_vid": "Cannot get wr_vid from <PERSON><PERSON>", "anki.wereader_card.cloze_grammar": "Cloze Grammar", "anki.wereader_card.continue_processing_highlights": "Continue processing highlights", "anki.wereader_card.cookie_cannot_empty": "<PERSON><PERSON> cannot be empty", "anki.wereader_card.cookie_expired_refreshed": "<PERSON><PERSON> expired, successfully refreshed <PERSON><PERSON>", "anki.wereader_card.cookie_placeholder": "<PERSON><PERSON>", "anki.wereader_card.cookie_refresh_error": "Failed to refresh <PERSON><PERSON>: @message", "anki.wereader_card.cookie_refresh_failed": "Failed to refresh <PERSON><PERSON>", "anki.wereader_card.cookie_refreshed_retry": "<PERSON><PERSON> refreshed, retrying note extraction", "anki.wereader_card.cookie_validation_error": "<PERSON><PERSON> validation failed: @error", "anki.wereader_card.cookie_validation_failed": "<PERSON><PERSON> validation failed", "anki.wereader_card.descending_order": "Descending", "anki.wereader_card.enable_separator": "Enable Separator", "anki.wereader_card.end_time": "End Time", "anki.wereader_card.export_format": "Export Format", "anki.wereader_card.export_type": "Export Type", "anki.wereader_card.extract_hot_failed": "Failed to extract popular highlights: @error", "anki.wereader_card.extract_notes_failed": "Failed to extract notes: @error", "anki.wereader_card.feature_description": "Supports creating flashcards from WeChat Reading notes, supports Q&A and cloze cards, also supports batch export of WeChat Reading notes", "anki.wereader_card.function_description": "Function Description", "anki.wereader_card.generate_excel_failed": "Failed to generate Excel file", "anki.wereader_card.get_all_hot_format_error": "Failed to get all popular highlights: incorrect data format", "anki.wereader_card.get_all_hot_success": "Successfully retrieved all popular highlights", "anki.wereader_card.get_book_info_failed": "Failed to get book info: @message", "anki.wereader_card.get_bookshelf_failed": "Failed to get bookshelf: @message", "anki.wereader_card.get_bookshelf_status_failed": "Failed to get bookshelf: @statusCode", "anki.wereader_card.get_chapters_failed": "Failed to get book chapters: @message", "anki.wereader_card.get_chapters_format_error": "Failed to get book chapters: incorrect data format", "anki.wereader_card.get_highlights_failed": "Failed to get highlights: @message", "anki.wereader_card.get_hot_failed": "Failed to get popular highlights: @message", "anki.wereader_card.get_hot_unknown_error": "Unknown error occurred while getting popular highlights: @error", "anki.wereader_card.get_notebook_failed": "Failed to get notebook: @message, @data, cookie: @cookie", "anki.wereader_card.get_progress_failed": "Failed to get reading progress: @message", "anki.wereader_card.get_reviews_failed": "Failed to get personal thoughts: @message", "anki.wereader_card.getting_all_hot": "Getting all @count popular highlights...", "anki.wereader_card.getting_hot_count": "Getting popular highlights count...", "anki.wereader_card.highlight_notes": "Highlight Notes", "anki.wereader_card.highlights_api_format_error": "Highlights API returned incorrect data format: @result", "anki.wereader_card.highlights_api_format_error_simple": "Highlights API returned incorrect data format", "anki.wereader_card.hot_api_format_error": "Popular highlights API returned incorrect data format", "anki.wereader_card.hot_highlights": "Popular Highlights", "anki.wereader_card.hot_highlights_format_error": "Failed to get popular highlights: incorrect data format", "anki.wereader_card.input_book": "Enter Book", "anki.wereader_card.input_cloze_grammar": "Enter Cloze Grammar", "anki.wereader_card.input_separator": "Enter Separator", "anki.wereader_card.input_tags": "Enter Tags", "anki.wereader_card.limit_time_range": "Limit Time Range", "anki.wereader_card.my_notes": "My Notes", "anki.wereader_card.no_hot_highlights": "No popular highlights, returning initial request result", "anki.wereader_card.note_card_tab": "Note to Cards", "anki.wereader_card.note_export_tab": "Note Export", "anki.wereader_card.note_source": "Note Source", "anki.wereader_card.note_type": "Note Type", "anki.wereader_card.output_directory": "Output Directory", "anki.wereader_card.people_highlighted": "@count people highlighted", "anki.wereader_card.processing": "Processing...", "anki.wereader_card.published_at": "Published at @time", "anki.wereader_card.published_thought_at": "Published thought at @time:", "anki.wereader_card.review_notes": "Review Notes", "anki.wereader_card.reviews_api_format_error": "Personal thoughts API returned incorrect data format: @result", "anki.wereader_card.reviews_api_format_error_simple": "Personal thoughts API returned incorrect data format", "anki.wereader_card.select_cloze_grammar": "Select Cloze Grammar", "anki.wereader_card.select_end_time": "Select End Time", "anki.wereader_card.select_output_directory": "Please select output directory", "anki.wereader_card.select_separator": "Select Separator", "anki.wereader_card.select_start_time": "Select Start Time", "anki.wereader_card.select_tags": "Select Tags", "anki.wereader_card.select_target_book": "Select Target Book", "anki.wereader_card.separator": "Separator", "anki.wereader_card.sort_by_chapter": "By Chapter", "anki.wereader_card.sort_by_create_time": "By Creation Time", "anki.wereader_card.sort_by_popularity": "By Popularity", "anki.wereader_card.sort_direction": "Sort Direction", "anki.wereader_card.sort_method": "Sort Method", "anki.wereader_card.start_time": "Start Time", "anki.wereader_card.target_book": "Target Book", "anki.wereader_card.title": "WeChat Reading Flashcards", "anki.word_card.answer_after_option": "Answer after options", "anki.word_card.answer_cloze": "Answer Cloze", "anki.word_card.answer_file_fields": "Answer file contains fields", "anki.word_card.answer_in_question": "Answer in question", "anki.word_card.at_least_one_cloze_grammar": "Select at least one cloze grammar", "anki.word_card.at_least_one_cloze_style": "Select at least one cloze style", "anki.word_card.blue_color": "Blue (#0000FF)", "anki.word_card.bold": "Bold", "anki.word_card.card_template": "Card Template", "anki.word_card.choice_tab": "Multiple Choice", "anki.word_card.cloze_grammar": "Cloze Grammar", "anki.word_card.cloze_style": "Cloze Style", "anki.word_card.cloze_tab": "C<PERSON>ze <PERSON>s", "anki.word_card.convert_to_html": "", "anki.word_card.extract_card": "", "anki.word_card.feature_description": "Supports creating flashcards from DOCX format files, supports cloze, Q&A, multiple choice, and true/false cards with multiple creation modes", "anki.word_card.file_path_not_exist": "File path does not exist", "anki.word_card.fixed_options_true_false": "True||False", "anki.word_card.free_guess": "Free Guess", "anki.word_card.function_description": "Function Description", "anki.word_card.green_color": "Green (#00FF00)", "anki.word_card.guru_import": "Guru <PERSON>", "anki.word_card.highlight_color": "Highlight Color", "anki.word_card.input_color_placeholder": "Enter color, e.g. #FF0000", "anki.word_card.input_subdeck_prefix": "Enter subdeck prefix", "anki.word_card.input_tags": "Enter Tags", "anki.word_card.italic": "Italic", "anki.word_card.judge_tab": "True/False", "anki.word_card.mask_all_guess_all": "Mask All Guess All", "anki.word_card.mask_all_guess_one": "Mask All Guess One", "anki.word_card.mask_one_guess_one": "Mask One Guess One", "anki.word_card.none": "None", "anki.word_card.normal_separation": "Normal Separation", "anki.word_card.one_cloze_per_card": "One Cloze Per Card", "anki.word_card.please_select_answer_file": "Please select answer file", "anki.word_card.please_select_file": "Please select file", "anki.word_card.please_select_file_not_directory": "Please select file, not directory", "anki.word_card.please_select_files": "Please select files!", "anki.word_card.please_select_kevin_choice_template": "Please select [Kevin Choice Card v2] template", "anki.word_card.please_select_kevin_text_cloze_template": "Please select [<PERSON> Cloze v3] template", "anki.word_card.please_select_question_answer_files": "Please select question and answer files!", "anki.word_card.please_select_question_file": "Please select question file", "anki.word_card.please_set_answers_field_pattern": "Please set Answers field pattern!", "anki.word_card.please_set_front_field_pattern": "Please set Front field pattern!", "anki.word_card.please_set_question_field_pattern": "Please set Question field pattern!", "anki.word_card.processing_question": "Processing question @current", "anki.word_card.qa_tab": "Q&A", "anki.word_card.question_answer_count_mismatch": "Question count (@questionCount) and answer count (@answerCount) mismatch in deck @deck!", "anki.word_card.question_answer_cross_file": "Questions and answers in separate files", "anki.word_card.question_answer_cross_file_choice": "Questions and answers in separate files", "anki.word_card.question_answer_same_file": "Questions and answers in same file", "anki.word_card.question_file_fields": "Question file contains fields", "anki.word_card.question_single_line": "Single line question", "anki.word_card.red_color": "Red (#FF0000)", "anki.word_card.regex_separation": "Regex Separation", "anki.word_card.restore_params": "Restore Parameters", "anki.word_card.save_params": "Save Parameters", "anki.word_card.select_answer_file_fields": "Select answer file fields", "anki.word_card.select_answer_file_fields_required": "Please select answer file fields", "anki.word_card.select_answer_file_placeholder": "Please select answer file", "anki.word_card.select_card_template": "Select Card Template", "anki.word_card.select_color": "Select Color", "anki.word_card.select_question_file_fields": "Select question file fields", "anki.word_card.select_question_file_fields_required": "Please select question file fields", "anki.word_card.select_subdeck_prefix": "Select Subdeck Prefix", "anki.word_card.select_tags": "Select Tags", "anki.word_card.strikethrough": "Strikethrough", "anki.word_card.subdeck_prefix": "Subdeck Prefix", "anki.word_card.text_color": "Text Color", "anki.word_card.text_color_option": "Text Color", "anki.word_card.text_highlight": "Text Highlight", "anki.word_card.title": "Word Flashcards", "anki.word_card.underline": "Underline", "anki.word_card.yellow_color": "Yellow (#FFFF00)", "ankiConfig.ankiConnectAddress": "AnkiConnect Address", "ankiConfig.ankiPath": "<PERSON><PERSON>", "ankiConfig.autoStartAnki": "Auto-start <PERSON><PERSON>", "ankiConfig.cardMode": "Card Creation Mode", "ankiConfig.ffmpegPath": "FFmpeg Path", "ankiConfig.outputDirectory": "Output Directory", "ankiConfig.outputDirectoryPlaceholder": "APKG output directory", "ankiConfig.pdfReaderPath": "PDF Reader Path", "ankiConfig.selectAnkiPath": "Please select the <PERSON><PERSON> path", "ankiConfig.selectCardMode": "Select card creation mode", "ankiConfig.selectFfmpegPath": "Please select the FFmpeg path", "ankiConfig.selectPdfReaderPath": "Please select the PDF reader path", "ankiConfig.title": "Preferences", "common.colorPicker.cancel": "Cancel", "common.colorPicker.confirm": "Confirm", "common.colorPicker.selectColor": "Select Color", "common.completed": "Completed", "common.error": "Error", "common.fileSelect.clearAll": "Clear All", "common.fileSelect.customDirectory": "Custom Directory", "common.fileSelect.directoryCannotBeEmpty": "Directory path cannot be empty", "common.fileSelect.directoryNotExist": "Directory does not exist", "common.fileSelect.fileCannotBeEmpty": "File path cannot be empty", "common.fileSelect.fileNotExist": "File does not exist", "common.fileSelect.overwriteOriginal": "Overwrite Original File", "common.fileSelect.pleaseSelectFiles": "Please select files in {format} format", "common.fileSelect.sameDirectory": "Same Directory", "common.fileSelect.selectDirectoryNotFile": "Please select a directory, not a file", "common.fileSelect.selectFileNotDirectory": "Please select a file, not a directory", "common.fileSelect.selectedFiles": "{count} files selected", "common.ui.emptyList": "List is empty", "common.ui.fieldMapping": "Field Mapping", "common.ui.keepPrefix": "Keep Prefix", "common.ui.matchMode": "Match Mode", "common.ui.searchPlaceholder": "Enter search query...", "common.ui.selectOrEnter": "Select or Enter", "common.ui.templateField": "Template Field", "home.sections.ankiCards": "<PERSON><PERSON>", "home.sections.ankiEnhance": "<PERSON><PERSON>", "home.sections.conversion": "Conversion Tools", "home.sections.notes": "Efficient Notes", "home.sections.pdfEdit": "PDF Editing", "home.tool.vocab_card": "Vocabulary Cards", "home.tools.aiCard": "AI Flashcards", "home.tools.ankiSync": "<PERSON><PERSON>", "home.tools.cardOcr": "Card OCR", "home.tools.cardTts": "Card TTS", "home.tools.card_media_manager": "Media Manager", "home.tools.deckManager": "Deck Manager", "home.tools.docxToHtml": "DOCX to HTML", "home.tools.epubToPdf": "EPUB to PDF", "home.tools.excelCard": "Excel Flashcards", "home.tools.flashNote": "Flash Note", "home.tools.imageCard": "Image Flashcards", "home.tools.image_ocr": "Image OCR", "home.tools.imgToPdf": "Image to PDF", "home.tools.markdownCard": "Markdown Flashcards", "home.tools.mdToHtml": "MD to HTML", "home.tools.mediaCard": "Multimedia Flashcards", "home.tools.mindmapCard": "Mind Map Flashcards", "home.tools.mobiToPdf": "MOBI to PDF", "home.tools.mubuCard": "Mubu Flashcards", "home.tools.pdfAnnot": "PDF Annotation", "home.tools.pdfBackground": "PDF Background", "home.tools.pdfBookmark": "PDF Bookmark", "home.tools.pdfCard": "PDF Flashcards", "home.tools.pdfCombine": "PDF Combine", "home.tools.pdfCrop": "PDF Crop", "home.tools.pdfCut": "PDF Cut", "home.tools.pdfDelete": "PDF Delete Pages", "home.tools.pdfEncrypt": "PDF Encrypt", "home.tools.pdfExpand": "PDF Expand", "home.tools.pdfExtract": "PDF Extract", "home.tools.pdfInsert": "PDF Insert", "home.tools.pdfMerge": "PDF Merge", "home.tools.pdfMeta": "PDF Metadata", "home.tools.pdfNote": "PDF Note", "home.tools.pdfPageNumber": "PDF Page Number", "home.tools.pdfReorder": "PDF Reorder Pages", "home.tools.pdfRotate": "PDF Rotate", "home.tools.pdfScale": "PDF Scale", "home.tools.pdfSplit": "PDF Split", "home.tools.pdfToDocx": "PDF to DOCX", "home.tools.pdfToImg": "PDF to Image", "home.tools.pdfToImgPdf": "PDF to Image-based PDF", "home.tools.pdfWatermark": "PDF Watermark", "home.tools.pdf_ocr": "PDF OCR", "home.tools.recoverPermission": "Recover Permissions", "home.tools.textCard": "Text Flashcards", "home.tools.videoNote": "Video Note", "home.tools.wechatReaderCard": "WeReader Flashcards", "home.tools.wordCard": "Word Card Creation", "hotkey.action.cancel": "Cancel", "hotkey.action.confirm": "Confirm", "hotkey.dialog.title": "Set Hotkey", "hotkey.error.enterShortcut": "Please enter a shortcut", "hotkey.error.requireModifier": "Shortcut requires a modifier key", "hotkey.error.title": "Error", "license.activation.failed": "Activation failed", "license.activation.success": "Activation successful", "license.faq.lifetime_10years.content": "Lifetime package activation codes are issued in 10-year cycles and can be renewed for free upon expiration.", "license.faq.lifetime_10years.title": "Why does the lifetime package activation code only last 10 years?", "license.faq.reinstall.content": "No, you only need to deactivate when you need to reinstall the system or change to a new device. If you lose activation status after reinstalling the software, just reactivate it. Repeated activation on the same device will not consume additional activation attempts.", "license.faq.reinstall.title": "Do I need to deactivate when uninstalling and reinstalling the software?", "license.faq.why_activate.content": "This software is feature-rich and powerful, with a beautiful and comfortable interface, multi-platform offline availability, no privacy concerns, clean and ad-free. Many PDF processing functions are directly available for free without restrictions, making it a great helper for your daily work and study. However, for the healthy development of the software and to continuously provide users with a better experience, some premium features are included (such as Anki card creation, video notes, etc.). You can show your support by purchasing an activation code and unlock all features of the software. Note: This software is only for personal use within reasonable limits. For commercial use that generates direct or indirect profits, please contact the author to purchase a separate commercial license.", "license.faq.why_activate.title": "Why do I need to activate?", "license.request.failed": "Failed", "license.trial.failed": "Failed to get trial authorization", "license.trial.success": "Trial activation successful", "license.unregister.failed": "Deactivation failed", "license.unregister.success": "Deactivation successful", "navigation.home": "Home", "navigation.settings": "Settings", "paywall.benefitsTitle": "Member-Exclusive Benefits", "paywall.features.aiCard": "Advanced AI Card Creation", "paywall.features.eudicCard": "<PERSON><PERSON>c <PERSON>", "paywall.features.excelCard": "Excel Card Creation", "paywall.features.fastSync": "High-Speed Sync Server", "paywall.features.imageCard": "Image Occlusion Card Creation", "paywall.features.logseqCard": "Logseq Card Creation", "paywall.features.markdownCard": "Markdown Card Creation", "paywall.features.mubuCard": "Mubu Card Creation", "paywall.features.multimediaCard": "Multimedia Card Creation", "paywall.features.multipleChoiceCard": "Batch Multiple Choice Card Creation", "paywall.features.notionCard": "Notion Flashcards", "paywall.features.obsidianCard": "Obsidian Flashcards", "paywall.features.pdfCard": "PDF Flashcards", "paywall.features.prioritySupport": "Priority Technical Support", "paywall.features.quickNote": "Quick Note Feature", "paywall.features.siyuanCard": "Siyuan Note Flashcards", "paywall.features.textCard": "Text Flashcards", "paywall.features.textToSpeech": "Text-to-Speech Feature", "paywall.features.videoNote": "Video Note Feature", "paywall.features.wechatReadCard": "WeChat Read Flashcards", "paywall.features.wordCard": "Word Flashcards", "paywall.features.wordListCard": "Word List Flashcards", "paywall.features.xmindCard": "Xmind Flashcards", "paywall.features.zhixiCard": "Zhixi MindMap Flashcards", "paywall.headerSubtitle": "Unlock all advanced features for effortless learning.", "paywall.headerTitle": "PDF Guru Anki Premium", "paywall.lifetimeSubscription": "Lifetime Membership", "paywall.monthlySubscription": "Monthly Subscription", "paywall.mostPopular": "Most Popular", "paywall.oneTimePurchase": "One-Time Purchase", "paywall.perMonth": "/month", "paywall.perYear": "/year", "paywall.privacyPolicy": "Privacy Policy", "paywall.purchase": "Purchase Now", "paywall.restorePurchase": "Restore Purchase", "paywall.selectPlan": "Please select a plan", "paywall.subscriptionDetails": "By clicking 'Purchase', you agree to our Terms of Service and Privacy Policy. Payment will be charged to your Apple ID account. You can cancel your subscription anytime in your Apple Store account settings at least 24 hours before the renewal date.", "paywall.subscriptionNotice": "Subscription Notice", "paywall.termsOfService": "Terms of Service", "paywall.title": "Purchase Membership", "paywall.yearlySubscription": "Yearly Subscription", "progress.button.open": "Open", "progress.button.play": "Play", "progress.button.share": "Share", "progress.dialog.title": "Progress", "progress.error.cannotGetButtonPosition": "Cannot get button position", "progress.error.cannotGetContext": "Cannot get context", "progress.error.cannotOpenFile": "Cannot open file", "progress.error.cannotOpenFolder": "Cannot open folder", "progress.error.cannotShareFile": "Cannot share file", "progress.error.filePathNotExist": "File path does not exist", "progress.error.shareDirectoryNotSupported": "Sharing directories is not supported", "progress.error.title": "Error", "progress.status.completed": "Completed", "progress.status.fileSavedTo": "File saved to", "progress.status.initializing": "Initializing", "progress.status.processing": "Processing", "progress.status.totalProgress": "Total Progress", "progress.time.elapsedTime": "Elapsed Time", "progress.time.startTime": "Start Time", "service.browser.cannotConnect": "Cannot connect to browser extension", "service.browser.cannotOpenVideo": "Cannot open video in browser, please check connection status", "service.browser.commandSent": "Command sent to browser extension", "service.browser.extensionIdentified": "Browser extension identified successfully", "service.browser.extensionInstallPrompt": "Please ensure the browser extension is installed and connected to the app", "service.browser.extensionRequired": "Browser extension connection required", "service.browser.navigationFailed": "Browser navigation failed", "service.browser.openingVideo": "Opening browser video", "service.browser.videoWillOpen": "Video will open in browser and jump to specified time", "service.screenshot.browserFailed": "Browser screenshot failed", "service.screenshot.dataEmpty": "Screenshot data is empty", "service.screenshot.failed": "Screenshot failed", "service.screenshot.saved": "Screenshot saved to clipboard", "service.timestamp.browserExtensionFailed": "Browser extension timestamp generation failed", "service.timestamp.generationFailed": "Timestamp generation failed", "service.timestamp.linkCopied": "Timestamp link copied", "service.timestamp.linkCopiedToClipboard": "Timestamp link copied to clipboard", "service.timestamp.linkSaved": "Link saved to clipboard", "service.video.cannotRetrieveInfo": "Cannot retrieve video information", "service.video.infoRetrievalFailed": "Video info retrieval failed", "service.video.jumpedToTime": "Jumped to specified time", "service.video.jumpedToTimestamp": "Browser video jumped to specified timestamp", "service.video.openedAndJumped": "Video successfully opened in browser and jumped to specified time", "service.video.openedInBrowser": "Video opened in browser", "settings.aboutAndHelp": "About & Help", "settings.activate": "Activate", "settings.cardMode.directAnki": "Direct Anki", "settings.cardMode.directAnkidroid": "Direct Ankidroid", "settings.cardMode.exportApkg": "Export Apkg", "settings.displayLanguage": "Display Language", "settings.language": "", "settings.launchAtStartup": "Launch at Startup", "settings.preferences": "Preferences", "settings.saved": "Setting<PERSON> saved", "settings.theme.dark": "Dark", "settings.theme.light": "Light", "settings.theme.system": "Follow System", "settings.themeSettings": "Theme Settings", "settings.userCenter": "User Center", "toolbox.annotation.annotationDelete": "Delete Annotations", "toolbox.annotation.annotationExport": "Export Annotations", "toolbox.annotation.annotationFile": "Annotation File", "toolbox.annotation.annotationFlatten": "Flatten Annotations", "toolbox.annotation.annotationImport": "Import Annotations", "toolbox.annotation.deleteTab": "Delete Annotations", "toolbox.annotation.description": "Import, export, or delete annotations in a PDF file.", "toolbox.annotation.exportFormat": "Export Format", "toolbox.annotation.exportTab": "Export Annotations", "toolbox.annotation.flattenTab": "Flatten Annotations", "toolbox.annotation.importFormat": "Import Format", "toolbox.annotation.importTab": "Import Annotations", "toolbox.annotation.title": "PDF Annotation", "toolbox.background.backgroundColor": "Background Color", "toolbox.background.backgroundImage": "Background Image", "toolbox.background.backgroundType": "Background Type", "toolbox.background.colorBackground": "Solid Color Background", "toolbox.background.completed": "Completed", "toolbox.background.description": "Add a solid color or image background to a PDF file.", "toolbox.background.fileNotExist": "File does not exist", "toolbox.background.generateBackgroundFailed": "Failed to generate background PDF", "toolbox.background.imageBackground": "Image Background", "toolbox.background.imageFileNotExist": "Image file does not exist", "toolbox.background.imageNotSelected": "No image selected", "toolbox.background.opacity": "Opacity", "toolbox.background.opacityPlaceholder": "Enter opacity, between 0 and 1", "toolbox.background.processingFile": "Processing file", "toolbox.background.scale": "Scale Ratio", "toolbox.background.scalePlaceholder": "Enter scale ratio", "toolbox.background.title": "PDF Background", "toolbox.background.unknownBackgroundType": "Unknown background type", "toolbox.background.xOffset": "X Offset", "toolbox.background.xOffsetPlaceholder": "Enter horizontal offset", "toolbox.background.yOffset": "Y Offset", "toolbox.background.yOffsetPlaceholder": "Enter vertical offset", "toolbox.bookmark.bookmarkFile": "Bookmark File", "toolbox.bookmark.bookmarkFileEmpty": "Bookmark file cannot be empty", "toolbox.bookmark.deleteBookmarks": "Delete Bookmarks", "toolbox.bookmark.deleteTab": "Delete Bookmarks", "toolbox.bookmark.description": "Import, export, or delete bookmarks in a PDF file.", "toolbox.bookmark.exportBookmarks": "Export Bookmarks", "toolbox.bookmark.exportFormat": "Export Format", "toolbox.bookmark.exportTab": "Export Bookmarks", "toolbox.bookmark.importBookmarks": "Import Bookmarks", "toolbox.bookmark.importTab": "Import Bookmarks", "toolbox.bookmark.title": "PDF Bookmark", "toolbox.combine.col_lr": "Column Priority (Left to Right)", "toolbox.combine.col_rl": "Column Priority (Right to Left)", "toolbox.combine.completed": "Completed", "toolbox.combine.description": "Combine multiple pages of the original PDF into a single page.", "toolbox.combine.landscape": "Landscape", "toolbox.combine.layout_order": "Layout Order", "toolbox.combine.numCols": "Number of Columns", "toolbox.combine.numColsPlaceholder": "Enter number of columns", "toolbox.combine.numRows": "Number of Rows", "toolbox.combine.numRowsPlaceholder": "Enter number of rows", "toolbox.combine.portrait": "Portrait", "toolbox.combine.row_lr": "Row Priority (Left to Right)", "toolbox.combine.row_rl": "Row Priority (Right to Left)", "toolbox.combine.title": "PDF Combine (N-up)", "toolbox.common.enterPageRange": "Please enter a valid page range, e.g., 1-3,5-7", "toolbox.common.error_with_msg": "Processing failed: {error}", "toolbox.common.failure": "Failed", "toolbox.common.fileProcessSuccess": "File processed successfully", "toolbox.common.fileSelect.error": "Please select a PDF file", "toolbox.common.functionDescription": "Function Description", "toolbox.common.inputFile": "Input File", "toolbox.common.inputFilePlaceholder": "Enter absolute file path, or drag and drop file here", "toolbox.common.operationFailed": "Operation failed", "toolbox.common.output.error": "Please select an output directory", "toolbox.common.outputDir": "Output Directory", "toolbox.common.outputDirectory": "Output Directory", "toolbox.common.outputLocation": "Output Location", "toolbox.common.pageRange": "Page Range", "toolbox.common.pageRangePlaceholder": "Enter page range, leave blank for all", "toolbox.common.process.completed": "Completed", "toolbox.common.process.failed": "Operation failed", "toolbox.common.process.processFailed": "File processing failed", "toolbox.common.process.running": "Processing file", "toolbox.common.process.success": "File processed successfully", "toolbox.common.processing": "Processing...", "toolbox.common.selectOutputLocation": "Select output location", "toolbox.common.selectPdfFiles": "Please select a PDF file", "toolbox.common.submit": "Submit", "toolbox.convert.common.inputFile": "Input File", "toolbox.convert.common.inputFilePlaceholder": "Enter absolute file path, or drag and drop file here", "toolbox.convert.common.outputDirectory": "Output Directory", "toolbox.convert.common.outputLocation": "Output Location", "toolbox.convert.common.selectOutputLocation": "Select output location", "toolbox.convert.common.submit": "Submit", "toolbox.convert.docx2html.description": "Convert DOCX to HTML.", "toolbox.convert.docx2html.title": "DOCX to HTML", "toolbox.convert.docx2pdf.description": "Convert DOCX to PDF.", "toolbox.convert.docx2pdf.title": "DOCX to PDF", "toolbox.convert.epub2pdf.description": "Convert EPUB to PDF.", "toolbox.convert.epub2pdf.title": "EPUB to PDF", "toolbox.convert.html2pdf.customFont": "Custom Font", "toolbox.convert.html2pdf.description": "Convert an HTML file to a PDF document.", "toolbox.convert.html2pdf.font": "Font", "toolbox.convert.html2pdf.fontFile": "Font File", "toolbox.convert.html2pdf.fontFilePlaceholder": "Enter absolute font file path, or drag and drop file here", "toolbox.convert.html2pdf.fontSize": "Font Size", "toolbox.convert.html2pdf.fontSizeError1": "Please enter font size, e.g., 12", "toolbox.convert.html2pdf.fontSizeError2": "Please enter a floating-point number", "toolbox.convert.html2pdf.fontSizePlaceholder": "Enter font size", "toolbox.convert.html2pdf.selectFont": "Select font", "toolbox.convert.html2pdf.title": "HTML to PDF", "toolbox.convert.img2pdf.description": "Convert images to PDF.", "toolbox.convert.img2pdf.mergeFiles": "Merge into one file", "toolbox.convert.img2pdf.orientation": "Paper Orientation", "toolbox.convert.img2pdf.paperSize": "Paper Size", "toolbox.convert.img2pdf.selectOrientation": "Please select orientation", "toolbox.convert.img2pdf.selectPaperSize": "Please select paper size", "toolbox.convert.img2pdf.sortBy": "Sort By", "toolbox.convert.img2pdf.sortDirection": "Sort Direction", "toolbox.convert.img2pdf.sortDirections.ascending": "Ascending", "toolbox.convert.img2pdf.sortDirections.descending": "Descending", "toolbox.convert.img2pdf.sortOptions.byCreateDate": "Creation Date", "toolbox.convert.img2pdf.sortOptions.byModDate": "Modification Date", "toolbox.convert.img2pdf.sortOptions.byName": "Filename", "toolbox.convert.img2pdf.sortOptions.byNumberPrefix": "Number Prefix in Filename", "toolbox.convert.img2pdf.sortOptions.byNumberSuffix": "Number Suffix in Filename", "toolbox.convert.img2pdf.sortOptions.bySelection": "Selection Order", "toolbox.convert.img2pdf.title": "Image to PDF", "toolbox.convert.md2html.description": "Convert Markdown to HTML.", "toolbox.convert.md2html.title": "MD to HTML", "toolbox.convert.md2pdf.description": "Convert a Markdown file to a PDF document.", "toolbox.convert.md2pdf.title": "Markdown to PDF", "toolbox.convert.mobi2pdf.description": "Convert MOBI to PDF.", "toolbox.convert.mobi2pdf.title": "MOBI to PDF", "toolbox.convert.ofd2pdf.description": "Convert OFD to PDF.", "toolbox.convert.ofd2pdf.title": "OFD to PDF", "toolbox.convert.pdf2docx.description": "Convert PDF to DOCX.", "toolbox.convert.pdf2docx.title": "PDF to DOCX", "toolbox.convert.pdf2img.advancedScaling": "Advanced Scaling Options", "toolbox.convert.pdf2img.autoScaleToA4": "Auto-scale oversized pages to A4", "toolbox.convert.pdf2img.description": "Convert specified pages of a PDF to images.", "toolbox.convert.pdf2img.grayscale": "Convert to Gray<PERSON><PERSON>", "toolbox.convert.pdf2img.maxHeightPixels": "Max Height (pixels)", "toolbox.convert.pdf2img.maxHeightPixelsPlaceholder": "Maximum height in pixels, default is 4000", "toolbox.convert.pdf2img.maxPixelsError": "Please enter a valid pixel value greater than 0", "toolbox.convert.pdf2img.maxWidthPixels": "<PERSON> (pixels)", "toolbox.convert.pdf2img.maxWidthPixelsPlaceholder": "Maximum width in pixels, default is 3000", "toolbox.convert.pdf2img.pageRange": "Page Range", "toolbox.convert.pdf2img.pageRangeError": "Please enter a valid page range, e.g., 1-3,5-7", "toolbox.convert.pdf2img.pageRangePlaceholder": "Enter page range, leave blank for all", "toolbox.convert.pdf2img.resolution": "Resolution (DPI)", "toolbox.convert.pdf2img.resolutionError": "Please enter a valid resolution, e.g., 300", "toolbox.convert.pdf2img.resolutionPlaceholder": "Enter resolution, default is 300", "toolbox.convert.pdf2img.title": "PDF to Image", "toolbox.convert.pdf2img_pdf.description": "Convert a PDF to an image-based PDF.", "toolbox.convert.pdf2img_pdf.title": "PDF to Image-based PDF", "toolbox.crop.cropType": "Crop Type", "toolbox.crop.description": "Crop pages in a PDF file.", "toolbox.crop.expandMode": "Expand Mode", "toolbox.crop.keepPaperSize": "Keep Page Size", "toolbox.crop.margin.bottom": "Bottom Margin", "toolbox.crop.margin.bottomPlaceholder": "Enter bottom margin", "toolbox.crop.margin.left": "Left Margin", "toolbox.crop.margin.leftPlaceholder": "Enter left margin", "toolbox.crop.margin.right": "Right Margin", "toolbox.crop.margin.rightPlaceholder": "Enter right margin", "toolbox.crop.margin.top": "Top Margin", "toolbox.crop.margin.topPlaceholder": "Enter top margin", "toolbox.crop.outputFormat": "Output Format", "toolbox.crop.stem_append": "_cropped", "toolbox.crop.title": "PDF Crop", "toolbox.crop.unit": "Unit", "toolbox.cropTypeOptions.annotate": "Rectangular Annotation Crop", "toolbox.cropTypeOptions.margin": "<PERSON><PERSON>", "toolbox.cut.custom.hBreakpoints": "Horizontal Breakpoints", "toolbox.cut.custom.hBreakpointsInvalid": "Please enter decimals between 0 and 1, separated by commas, e.g., 0.4,0.7", "toolbox.cut.custom.hBreakpointsPlaceholder": "Enter horizontal breakpoints, e.g., 0.4,0.7", "toolbox.cut.custom.vBreakpoints": "Vertical Breakpoints", "toolbox.cut.custom.vBreakpointsInvalid": "Please enter decimals between 0 and 1, separated by commas, e.g., 0.4,0.7", "toolbox.cut.custom.vBreakpointsPlaceholder": "Enter vertical breakpoints, e.g., 0.4,0.7", "toolbox.cut.cutOptions.custom": "Custom segmentation", "toolbox.cut.cutOptions.grid": "Grid segmentation", "toolbox.cut.cutOptions.page": "Page segmentation", "toolbox.cut.cutType": "Cut Type", "toolbox.cut.description": "Cut specified pages of the original PDF into multiple pages.", "toolbox.cut.grid.numCols": "Number of Columns", "toolbox.cut.grid.numColsPlaceholder": "Enter number of columns", "toolbox.cut.grid.numColsRequired": "Please enter number of columns, e.g., 1", "toolbox.cut.grid.numRows": "Number of Rows", "toolbox.cut.grid.numRowsPlaceholder": "Enter number of rows", "toolbox.cut.grid.numRowsRequired": "Please enter number of rows, e.g., 1", "toolbox.cut.page.margin.bottom": "Bottom Margin", "toolbox.cut.page.margin.bottomPlaceholder": "Enter bottom margin", "toolbox.cut.page.margin.invalid": "Please enter a number greater than or equal to 0", "toolbox.cut.page.margin.left": "Left Margin", "toolbox.cut.page.margin.leftPlaceholder": "Enter left margin", "toolbox.cut.page.margin.right": "Right Margin", "toolbox.cut.page.margin.rightPlaceholder": "Enter right margin", "toolbox.cut.page.margin.top": "Top Margin", "toolbox.cut.page.margin.topPlaceholder": "Enter top margin", "toolbox.cut.page.orientation": "Orientation", "toolbox.cut.page.pageSize": "Paper Size", "toolbox.cut.page.selectOrientation": "Select orientation", "toolbox.cut.page.selectPageSize": "Select paper size", "toolbox.cut.title": "PDF Cut", "toolbox.decrypt.password": "Password", "toolbox.decrypt.passwordPlaceholder": "Please enter password", "toolbox.delete.blankTab": "Blank Pages", "toolbox.delete.description": "Delete specified pages from a PDF.", "toolbox.delete.stem_append": "Page Deletion", "toolbox.delete.title": "PDF Delete Pages", "toolbox.encrypt.decryptTab": "Remove Password", "toolbox.encrypt.description": "Encrypt or decrypt a PDF file.", "toolbox.encrypt.encryptTab": "Set Password", "toolbox.encrypt.fileNameAppend.decrypt": "_decrypted", "toolbox.encrypt.fileNameAppend.encrypt": "_encrypted", "toolbox.encrypt.mode.decrypt": "Decrypt", "toolbox.encrypt.mode.encrypt": "Encrypt", "toolbox.encrypt.passwordError.mismatch": "User passwords do not match", "toolbox.encrypt.passwordError.permissionMismatch": "Permissions passwords do not match", "toolbox.encrypt.passwordError.permissionRequired": "Please enter permissions password", "toolbox.encrypt.passwordError.required": "Please enter user password", "toolbox.encrypt.passwordError.validation": "Password validation failed", "toolbox.encrypt.passwordType": "Password Type", "toolbox.encrypt.passwordTypeRequired": "Select at least one password type", "toolbox.encrypt.permission.assembleDocument": "Assembling", "toolbox.encrypt.permission.copyContent": "Copying Content", "toolbox.encrypt.permission.editAnnotations": "Editing Annotations", "toolbox.encrypt.permission.editContent": "Editing Content", "toolbox.encrypt.permission.fillFields": "Filling Forms", "toolbox.encrypt.permission.print": "Printing", "toolbox.encrypt.title": "PDF Encrypt", "toolbox.encrypt.type.permission": "Permissions Password", "toolbox.encrypt.type.user": "User Password", "toolbox.encrypt.user.confirmEmpty": "Confirmation password cannot be empty", "toolbox.encrypt.user.confirmPassword": "Confirm User Password", "toolbox.encrypt.user.confirmPasswordPlaceholder": "Re-enter user password", "toolbox.encrypt.user.password": "User Password", "toolbox.encrypt.user.passwordEmpty": "Password cannot be empty", "toolbox.encrypt.user.passwordMismatch": "Passwords do not match", "toolbox.encrypt.user.passwordPlaceholder": "Enter user password", "toolbox.encrypt.user.passwordTooShort": "Password must be at least 6 characters long", "toolbox.expand.blank.margin.bottom": "Bottom Margin", "toolbox.expand.blank.margin.bottomPlaceholder": "Enter bottom margin", "toolbox.expand.blank.margin.invalid": "Please enter a number greater than or equal to 0", "toolbox.expand.blank.margin.left": "Left Margin", "toolbox.expand.blank.margin.leftPlaceholder": "Enter left margin", "toolbox.expand.blank.margin.right": "Right Margin", "toolbox.expand.blank.margin.rightPlaceholder": "Enter right margin", "toolbox.expand.blank.margin.top": "Top Margin", "toolbox.expand.blank.margin.topPlaceholder": "Enter top margin", "toolbox.expand.blank.unit": "Unit", "toolbox.expand.description": "Expand specified pages of the original PDF with given parameters.", "toolbox.expand.direction.bottom": "Bottom", "toolbox.expand.direction.left": "Left", "toolbox.expand.direction.right": "Right", "toolbox.expand.direction.top": "Top", "toolbox.expand.expandType": "Expand Type", "toolbox.expand.file.bgFile": "Background File", "toolbox.expand.file.bgFilePlaceholder": "Enter absolute background file path, or drag and drop file here", "toolbox.expand.file.direction": "Direction", "toolbox.expand.fileNameAppend": "_expanded", "toolbox.expand.mode.blank": "Expand with Blank Space", "toolbox.expand.mode.file": "Expand with File", "toolbox.expand.title": "PDF Expand", "toolbox.expand.unit.pt": "Pixels (pt)", "toolbox.expand.unit.ratio": "<PERSON><PERSON>", "toolbox.extract.description": "Extract pages, text, images, etc., from a PDF.", "toolbox.extract.extractMode": "Extraction Mode", "toolbox.extract.fileNameAppend": "_pages_extracted", "toolbox.extract.imageAppend": "_images_extracted", "toolbox.extract.mode.image": "Images", "toolbox.extract.mode.page": "Pages", "toolbox.extract.mode.text": "Text", "toolbox.extract.textAppend": "_text_extracted", "toolbox.extract.title": "PDF Extract", "toolbox.extract.unsupported": "Not Supported", "toolbox.extract.unsupportedMessage": "Image extraction feature is not yet available.", "toolbox.insert.description": "Insert blank pages or a specified file into the original PDF.", "toolbox.insert.fileNameAppend": "_inserted", "toolbox.insert.insertCount": "Number to Insert", "toolbox.insert.insertFile": "File to Insert", "toolbox.insert.insertPage": "Insert at Page Number", "toolbox.insert.insertPosition": "Insert Position", "toolbox.insert.insertType": "Insert Type", "toolbox.insert.mode.blank": "Insert Blank Pages", "toolbox.insert.mode.file": "Insert File", "toolbox.insert.orientation": "Paper direction", "toolbox.insert.orientationOptions.landscape": "Landscape", "toolbox.insert.orientationOptions.portrait": "Portrait", "toolbox.insert.paperSize": "Paper Size", "toolbox.insert.position.after_all": "After all pages", "toolbox.insert.position.after_first": "After first page", "toolbox.insert.position.after_last": "After last page", "toolbox.insert.position.after_page": "After specified page", "toolbox.insert.position.before_all": "Before all pages", "toolbox.insert.position.before_even": "Before even pages", "toolbox.insert.position.before_first": "Before first page", "toolbox.insert.position.before_last": "Before last page", "toolbox.insert.position.before_odd": "Before odd pages", "toolbox.insert.position.before_page": "Before specified page", "toolbox.insert.title": "PDF Insert", "toolbox.merge.atLeastTwoFilesRequired": "At least two files required", "toolbox.merge.description": "Merge multiple PDF files into one, or merge multiple pages of a single PDF into one page.", "toolbox.merge.direction": "Sort Direction", "toolbox.merge.directionOptions.ascending": "Ascending", "toolbox.merge.directionOptions.descending": "Descending", "toolbox.merge.fileNameAppend.multiFile": "_merged", "toolbox.merge.mergeType": "Merge Type", "toolbox.merge.mode.file": "Merge Multiple Files", "toolbox.merge.mode.page": "Merge <PERSON>s", "toolbox.merge.page_merge_stem_append": "_page_merged", "toolbox.merge.sortBy": "Sort By", "toolbox.merge.sortByOptions.createDate": "Creation Date", "toolbox.merge.sortByOptions.modDate": "Modification Date", "toolbox.merge.sortByOptions.name": "Filename", "toolbox.merge.sortByOptions.numberPrefix": "Number Prefix in Filename", "toolbox.merge.sortByOptions.numberSuffix": "Number Suffix in Filename", "toolbox.merge.sortByOptions.selection": "Selection Order", "toolbox.merge.title": "PDF Merge", "toolbox.meta.author": "Author", "toolbox.meta.authorPlaceholder": "Enter author", "toolbox.meta.creationDate": "Creation Date", "toolbox.meta.creationDatePlaceholder": "Enter creation date, e.g., 2021-01-01 00:00:00", "toolbox.meta.creator": "Creator", "toolbox.meta.creatorPlaceholder": "Enter creator", "toolbox.meta.description": "Set the metadata for a PDF file.", "toolbox.meta.keywords": "Keywords", "toolbox.meta.keywordsPlaceholder": "Enter keywords", "toolbox.meta.modDate": "Modification Date", "toolbox.meta.modDatePlaceholder": "Enter modification date, e.g., 2021-01-01 00:00:00", "toolbox.meta.producer": "Producer", "toolbox.meta.producerPlaceholder": "Enter producer", "toolbox.meta.subject": "Subject", "toolbox.meta.subjectPlaceholder": "Enter subject", "toolbox.meta.title": "PDF Metadata", "toolbox.meta.titlePlaceholder": "Enter title", "toolbox.meta.title_field": "Title", "toolbox.ocr.description": "Extract text from PDF files using OCR technology.", "toolbox.ocr.title": "PDF OCR", "toolbox.pageNumber.alignment": "Alignment", "toolbox.pageNumber.customFont": "Custom Font", "toolbox.pageNumber.customStyle": "Custom Style", "toolbox.pageNumber.customStylePlaceholder": "Enter custom style, %p for current page, %P for total pages, e.g., %p/%P", "toolbox.pageNumber.description": "Add page numbers to a PDF file.", "toolbox.pageNumber.enterFloatNumber": "Please enter a floating-point number", "toolbox.pageNumber.fontColor": "Font Color", "toolbox.pageNumber.fontFamily": "Font", "toolbox.pageNumber.fontFile": "Font File", "toolbox.pageNumber.fontFilePlaceholder": "Enter absolute font file path, or drag and drop file here", "toolbox.pageNumber.fontSize": "Font Size", "toolbox.pageNumber.fontSizePlaceholder": "Enter font size", "toolbox.pageNumber.fontSizeRequired": "Please enter font size, e.g., 12", "toolbox.pageNumber.margin.bottom": "Bottom Margin", "toolbox.pageNumber.margin.bottomPlaceholder": "Enter bottom margin", "toolbox.pageNumber.margin.bottomRequired": "Please enter bottom margin", "toolbox.pageNumber.margin.invalid": "Please enter a number greater than or equal to 0", "toolbox.pageNumber.margin.left": "Left Margin", "toolbox.pageNumber.margin.leftPlaceholder": "Enter left margin", "toolbox.pageNumber.margin.leftRequired": "Please enter left margin", "toolbox.pageNumber.margin.right": "Right Margin", "toolbox.pageNumber.margin.rightPlaceholder": "Enter right margin", "toolbox.pageNumber.margin.rightRequired": "Please enter right margin", "toolbox.pageNumber.margin.top": "Top Margin", "toolbox.pageNumber.margin.topPlaceholder": "Enter top margin", "toolbox.pageNumber.margin.topRequired": "Please enter top margin", "toolbox.pageNumber.numberPosition": "Number Position", "toolbox.pageNumber.opacity": "Opacity", "toolbox.pageNumber.opacityPlaceholder": "Enter opacity, between 0 and 1", "toolbox.pageNumber.opacityRequired": "Please enter opacity, e.g., 0.5", "toolbox.pageNumber.selectAlignment": "Select alignment", "toolbox.pageNumber.selectFontFamily": "Select font", "toolbox.pageNumber.selectNumberPosition": "Select number position", "toolbox.pageNumber.selectStyle": "Select style", "toolbox.pageNumber.style": "Number Style", "toolbox.pageNumber.title": "PDF Page Number", "toolbox.pdf_ocr.is_merge_mode": "Merge <PERSON>", "toolbox.pdf_ocr.is_show_page_sep": "Show Page Separator", "toolbox.permission.description": "Restore permissions of a protected PDF file.", "toolbox.permission.title": "Restore Permissions", "toolbox.reorder.description": "Reorder pages in a PDF file.", "toolbox.reorder.newPageOrder": "New Page Order", "toolbox.reorder.newPageOrderPlaceholder": "Enter new page order, e.g., to swap the first two pages, enter: 2,1,3-N", "toolbox.reorder.reorder": "Page reordering", "toolbox.reorder.title": "PDF Reorder", "toolbox.replace.blankTab": "Replace with <PERSON><PERSON>", "toolbox.replace.description": "Replace pages in a PDF file.", "toolbox.replace.fileTab": "Replace with File", "toolbox.replace.title": "PDF Replace", "toolbox.rotate.description": "Rotate pages in a PDF file.", "toolbox.rotate.rotationAngle": "Rotation Angle", "toolbox.rotate.title": "PDF Rotate", "toolbox.scale.description": "Adjust the page size of a PDF.", "toolbox.scale.enterPositiveNumber": "Please enter a number greater than or equal to 0", "toolbox.scale.height": "Height", "toolbox.scale.heightPlaceholder": "Enter page height (pt)", "toolbox.scale.heightRequired": "Please enter height", "toolbox.scale.paperSize": "Paper Size", "toolbox.scale.scaleRatio": "Scale Ratio", "toolbox.scale.scaleRatioPlaceholder": "Enter scale ratio, e.g., 2", "toolbox.scale.scaleRatioRequired": "Please enter a scale ratio", "toolbox.scale.scaleType": "Scale Type", "toolbox.scale.scaleTypeOptions.custom": "Custom Size", "toolbox.scale.scaleTypeOptions.fixed": "Fixed Size", "toolbox.scale.scaleTypeOptions.ratio": "Scale by Ratio", "toolbox.scale.selectPaperSize": "Select paper size", "toolbox.scale.stem_append": "_scaled", "toolbox.scale.title": "PDF Scale", "toolbox.scale.width": "<PERSON><PERSON><PERSON>", "toolbox.scale.widthPlaceholder": "Enter page width (pt)", "toolbox.scale.widthRequired": "Please enter width", "toolbox.split.bookmarkLevel": "Bookmark Level", "toolbox.split.bookmarkSplit": "Split by Bookmark", "toolbox.split.chunkSize": "Chunk Size", "toolbox.split.chunkSizeGreaterThanZero": "Chunk size must be greater than 0", "toolbox.split.chunkSizePlaceholder": "Enter chunk size", "toolbox.split.chunkSizeRequired": "Please enter chunk size, e.g., 10", "toolbox.split.customSplit": "Custom Split", "toolbox.split.customSplitSuffix": "Custom split", "toolbox.split.description": "Splits a PDF file into multiple PDF files. Supports uniform and custom splitting.", "toolbox.split.desktopOnly": "This feature is desktop-only", "toolbox.split.enterInteger": "Please enter an integer", "toolbox.split.level1": "Level 1 Heading", "toolbox.split.level2": "Level 2 Heading", "toolbox.split.level3": "Level 3 Heading", "toolbox.split.pleaseSelectFile": "Please select a file", "toolbox.split.selectBookmarkLevel": "Select bookmark level", "toolbox.split.splitSuffix": "Split", "toolbox.split.splitType": "Split Type", "toolbox.split.title": "PDF Split", "toolbox.split.uniformSplit": "Uniform Split", "toolbox.split.uniformSplitSuffix": "Uniform split", "toolbox.validation.enterFloat": "Please enter a floating point number", "toolbox.validation.enterInteger": "Please enter an integer", "toolbox.validation.enterNonNegativeNumber": "Please enter a number greater than or equal to 0", "toolbox.validation.mustBeGreaterThanZero": "Must be greater than 0", "toolbox.watermark.addTab": "Add Watermark", "toolbox.watermark.advancedOptions": "Advanced Options", "toolbox.watermark.createWatermarkImageFailed": "Failed to create watermark image", "toolbox.watermark.description": "Add or remove watermarks from PDF files.", "toolbox.watermark.desktopOnlyFeature": "This feature is only available on desktop", "toolbox.watermark.detectWatermarkFailed": "Failed to detect watermark", "toolbox.watermark.fileNotExist": "Input file does not exist", "toolbox.watermark.fontFamilyList.sourceHanSansSC": "Source Han Sans SC", "toolbox.watermark.image.file": "Image File", "toolbox.watermark.image.opacity": "Opacity", "toolbox.watermark.image.position": "Position", "toolbox.watermark.image.rotation": "Rotation Angle", "toolbox.watermark.image.scale": "Scale Ratio", "toolbox.watermark.imageDataEmpty": "Image data is empty", "toolbox.watermark.invalidWatermarkIndex": "Invalid watermark index format", "toolbox.watermark.limit_regin": "Limit Region", "toolbox.watermark.lower_bounds": "Lower Threshold", "toolbox.watermark.lower_bounds_placeholder": "Enter lower threshold", "toolbox.watermark.lower_bounds_required": "Please enter lower threshold", "toolbox.watermark.noValidRectangleAnnotation": "No valid rectangle annotations found on the specified pages", "toolbox.watermark.notSupport": "Not supported yet", "toolbox.watermark.readImageDataFailed": "Failed to read image data", "toolbox.watermark.removeTab": "Remove Watermark", "toolbox.watermark.removeWatermarkFailed": "Failed to remove watermark", "toolbox.watermark.savePdfFailed": "Failed to save PDF", "toolbox.watermark.stepOptions.step1": "Step 1: Detect Watermark Indices", "toolbox.watermark.stepOptions.step2": "Step 2: Remove Watermarks", "toolbox.watermark.step_option": "Step", "toolbox.watermark.text.content": "Watermark Text", "toolbox.watermark.text.customFont": "Custom Font", "toolbox.watermark.text.fontColor": "Font Color", "toolbox.watermark.text.fontFamily": "Font", "toolbox.watermark.text.fontFile": "Font File", "toolbox.watermark.text.fontSize": "Font Size", "toolbox.watermark.text.opacity": "Opacity", "toolbox.watermark.text.position": "Position", "toolbox.watermark.text.rotation": "Rotation Angle", "toolbox.watermark.text.selectFontFamily": "Select font", "toolbox.watermark.text.target_color": "Target Color", "toolbox.watermark.title": "PDF Watermark", "toolbox.watermark.type": "Watermark Type", "toolbox.watermark.typeOptions.image": "Image", "toolbox.watermark.typeOptions.text": "Text", "toolbox.watermark.upper_bounds": "Upper Threshold", "toolbox.watermark.upper_bounds_invalid": "Please enter an integer", "toolbox.watermark.upper_bounds_placeholder": "Enter upper threshold", "toolbox.watermark.upper_bounds_range": "Please enter an integer between 0-255", "toolbox.watermark.upper_bounds_required": "Please enter upper threshold", "toolbox.watermark.watermarkImageNotExist": "Watermark image path does not exist", "toolbox.watermark.watermarkImageRequired": "Please select a watermark image", "toolbox.watermark.watermarkIndexRequired": "Please enter watermark index", "toolbox.watermark.watermarkPageRequired": "Please specify the watermark page number", "toolbox.watermark.watermarkRemoveTypeList.content": "Content Watermark", "toolbox.watermark.watermarkRemoveTypeList.edit_text": "(Editable) Text Watermark", "toolbox.watermark.watermarkRemoveTypeList.image": "Image Watermark", "toolbox.watermark.watermarkRemoveTypeList.mask": "Mask Watermark", "toolbox.watermark.watermarkRemoveTypeList.path": "Path Watermark", "toolbox.watermark.watermarkRemoveTypeList.pixel": "Pixel Watermark", "toolbox.watermark.watermarkRemoveTypeList.type": "Type Watermark", "toolbox.watermark.watermarkTextRequired": "Please enter watermark text", "toolbox.watermark.wm_index": "Watermark Index", "toolbox.watermark.wm_index_placeholder": "Enter watermark index", "toolbox.watermark.wm_page_number": "Page with Watermark", "toolbox.watermark.wm_page_number_invalid": "Please enter only one page number, e.g., 1", "toolbox.watermark.wm_page_number_placeholder": "Enter page number with watermark, e.g., 1", "toolbox.watermark.wm_page_number_required": "Please enter watermark page number", "toolbox.watermark.wm_text_cannot_empty": "Watermark text cannot be empty", "toolbox.watermark.wm_text_to_remove": "Watermark Text", "toolbox.watermark.wm_text_to_remove_placeholder": "Enter watermark text to remove", "userCenter.annually": "Annual Member", "userCenter.buyMembership": "Purchase Membership", "userCenter.expiryDate": "Expiration Date", "userCenter.lifetime": "Lifetime Member", "userCenter.memberInfo": "Membership Info", "userCenter.memberType": "Membership Type", "userCenter.monthly": "Monthly Member", "userCenter.noMember": "None", "userCenter.title": "User Center", "userCenter.unknown": "Unknown", "videoNotes.action.delete": "Delete", "videoNotes.action.select": "Select", "videoNotes.cloze.freeGuess": "Free Guess", "videoNotes.cloze.maskAllGuessAll": "Mask All Guess All", "videoNotes.cloze.maskAllGuessOne": "Mask All Guess One", "videoNotes.cloze.maskOneGuessOne": "Mask One Guess One", "videoNotes.cloze.scratchGuess": "<PERSON><PERSON><PERSON>", "videoNotes.debug.debugProcessError": "Debug process error", "videoNotes.debug.serverStatusInfo": "Server status info", "videoNotes.debug.webSocketDebugComplete": "WebSocket debug complete", "videoNotes.debug.webSocketDebugFailed": "WebSocket debug failed", "videoNotes.defaults.deckName": "Video Notes", "videoNotes.dialog.addBilibiliVideo": "Add Bilibili Video", "videoNotes.dialog.addNetworkVideo": "Add Network Video", "videoNotes.dialog.cancel": "Cancel", "videoNotes.dialog.confirm": "Confirm", "videoNotes.dialog.selectRootFolder": "Select Root Folder", "videoNotes.dialog.selectStorageLocation": "Select Storage Location", "videoNotes.dialog.selectStorageLocationContent": "Please select a storage location", "videoNotes.dialog.videoTitle": "Video Title", "videoNotes.dialog.videoTitleHint": "Enter video title", "videoNotes.dialog.videoUrl": "Video URL", "videoNotes.dialog.videoUrlHint": "Enter video URL", "videoNotes.empty.addVideoPrompt": "Add videos to start taking notes", "videoNotes.empty.playlistEmpty": "Playlist is empty", "videoNotes.error.annotationFailed": "Annotation failed", "videoNotes.error.cannotConnectToBrowserPlugin": "Cannot connect to browser plugin", "videoNotes.error.cannotSaveScreenshot": "Cannot save screenshot", "videoNotes.error.cannotVerifyBrowserExtension": "Cannot verify browser extension", "videoNotes.error.checkServerStartup": "Please check server startup", "videoNotes.error.clozeImageFailed": "<PERSON><PERSON><PERSON> image failed", "videoNotes.error.connectionCheckFailed": "Connection check failed", "videoNotes.error.customTemplateEmpty": "Custom template cannot be empty", "videoNotes.error.deckNameEmpty": "Deck name cannot be empty", "videoNotes.error.filePermissionRequired": "File permission required", "videoNotes.error.ocrInitFailed": "OCR initialization failed", "videoNotes.error.operationFailed": "Operation failed", "videoNotes.error.playPauseCommandFailed": "Play/pause command failed", "videoNotes.error.processVideoError": "Video processing error", "videoNotes.error.saveFailed": "Save failed", "videoNotes.error.screenshotFailed": "Screenshot failed", "videoNotes.error.screenshotRequestFailed": "Screenshot request failed", "videoNotes.error.setRootDirectoryFirst": "Please set root directory first", "videoNotes.error.textExtractionFailed": "Text extraction failed", "videoNotes.error.timestampRequestFailed": "Timestamp request failed", "videoNotes.error.title": "Error", "videoNotes.error.webSocketServerNotRunning": "WebSocket server not running", "videoNotes.file.savePlaylist": "Save Playlist", "videoNotes.file.selectPlaylist": "Select Playlist", "videoNotes.linkFormat.custom": "Custom", "videoNotes.menu.openLocal": "Open Local Video", "videoNotes.menu.openNetwork": "Open Network Video", "videoNotes.menu.openPlaylist": "Open Playlist", "videoNotes.menu.preferences": "Preferences", "videoNotes.message.annotationCopied": "Annotation copied", "videoNotes.message.settingsSaved": "Setting<PERSON> saved", "videoNotes.message.success": "Success", "videoNotes.message.videoAdded": "Video added", "videoNotes.navigation.playlist": "Playlist", "videoNotes.navigation.subtitle": "Subtitle", "videoNotes.notification.addVideoFirst": "Please add a video first", "videoNotes.notification.annotationFailed": "Annotation failed", "videoNotes.notification.playerSettingsUpdated": "Player settings updated", "videoNotes.notification.playerSwitchedTo": "Player switched to", "videoNotes.notification.screenshotCopyFailed": "Failed to copy screenshot", "videoNotes.notification.screenshotCopySuccess": "Screenshot copied successfully", "videoNotes.notification.timestampCopyFailed": "Failed to copy timestamp", "videoNotes.notification.timestampCopySuccess": "Timestamp copied successfully", "videoNotes.player.browser": "Browser Player", "videoNotes.player.builtin": "Built-in Player", "videoNotes.playlist.empty": "Playlist is empty", "videoNotes.playlist.loadError": "Error loading playlist", "videoNotes.playlist.loadFailed": "Failed to load playlist", "videoNotes.playlist.loadSuccess": "Playlist loaded successfully", "videoNotes.playlist.loaded": "Playlist loaded", "videoNotes.playlist.saveFailed": "Failed to save playlist", "videoNotes.playlist.saved": "Playlist saved", "videoNotes.progress.generatingTimestamp": "Generating timestamp", "videoNotes.progress.gettingScreenshot": "Getting screenshot", "videoNotes.progress.pleaseWait": "Please wait", "videoNotes.settings.anki.defaultClozeMode": "De<PERSON>ult <PERSON>", "videoNotes.settings.anki.defaultClozeModeePlaceholder": "Select default cloze mode", "videoNotes.settings.anki.defaultDeck": "Default <PERSON>", "videoNotes.settings.anki.defaultDeckPlaceholder": "Enter default deck name", "videoNotes.settings.anki.defaultTags": "Default Tags", "videoNotes.settings.anki.defaultTagsPlaceholder": "Enter default tags, separated by commas", "videoNotes.settings.anki.oneClozePeCard": "One Cloze Per Card", "videoNotes.settings.backlink.autoPaste": "Auto Paste", "videoNotes.settings.backlink.customTemplate": "Custom Template", "videoNotes.settings.backlink.customTemplatePlaceholder": "Enter custom template", "videoNotes.settings.backlink.enablePathEncoding": "Enable Path Encoding", "videoNotes.settings.backlink.linkFormat": "Link Format", "videoNotes.settings.backlink.linkFormatPlaceholder": "Enter link format", "videoNotes.settings.backlink.pauseAfterCopyLink": "Pause After Copy Link", "videoNotes.settings.backlink.pauseAfterScreenshot": "Pause After Screenshot", "videoNotes.settings.backlink.rootFolder": "Root Folder", "videoNotes.settings.backlink.rootFolderPlaceholder": "Select root folder", "videoNotes.settings.backlink.useRelativePath": "Use Relative Path", "videoNotes.settings.category.ankiSettings": "<PERSON><PERSON>", "videoNotes.settings.category.backlinkSettings": "Backlink Settings", "videoNotes.settings.category.playbackControl": "Playback Control", "videoNotes.settings.category.shortcutSettings": "Shortcut Settings", "videoNotes.settings.player.brightnessGesture": "Brightness Gesture", "videoNotes.settings.player.defaultPlayer": "Default Player", "videoNotes.settings.player.defaultSpeedValue": "Default Speed Value", "videoNotes.settings.player.doubleTapPause": "Double Tap to Pause", "videoNotes.settings.player.doubleTapSeek": "Double Tap to Seek", "videoNotes.settings.player.longPressSpeed": "Long Press Speed", "videoNotes.settings.player.seekSeconds": "Seek Seconds", "videoNotes.settings.player.showMiniProgress": "Show Mini Progress", "videoNotes.settings.player.volumeGesture": "Volume Gesture", "videoNotes.settings.shortcuts.annotScreenshot": "Annotate Screenshot", "videoNotes.settings.shortcuts.backward15": "Backward 15s", "videoNotes.settings.shortcuts.backward5": "Backward 5s", "videoNotes.settings.shortcuts.copyScreenshot": "Copy Screenshot", "videoNotes.settings.shortcuts.copyTimestamp": "Copy Timestamp", "videoNotes.settings.shortcuts.disableAll": "Disable All", "videoNotes.settings.shortcuts.forward15": "Forward 15s", "videoNotes.settings.shortcuts.forward5": "Forward 5s", "videoNotes.settings.shortcuts.playPause": "Play/Pause", "videoNotes.shortcuts.allDisabled": "All shortcuts disabled", "videoNotes.shortcuts.allEnabled": "All shortcuts enabled", "videoNotes.status.notSet": "Not Set", "videoNotes.title.main": "Video Notes", "videoNotes.title.settings": "Settings", "videoNotes.tooltip.clearPlaylist": "Clear Playlist", "videoNotes.tooltip.openLocal": "Open Local Video", "videoNotes.tooltip.openNetwork": "Open Network Video", "videoNotes.tooltip.openPlaylist": "Open Playlist", "videoNotes.tooltip.savePlaylist": "Save Playlist", "videoNotes.validation.directoryNotExists": "Directory does not exist", "videoNotes.validation.rootFolderEmpty": "Root folder cannot be empty", "videoNotes.validation.selectDirectory": "Please select a directory", "videoNotes.validation.selectDirectoryNotFile": "Please select a directory, not a file", "watermark.completion.detectionCompleted": "Content watermark detection completed", "watermark.completion.imageWatermarkDetectionCompleted": "Image watermark detection completed", "watermark.completion.pathWatermarkDetectionCompleted": "Path watermark detection completed", "watermark.completion.pdfToImageCompleted": "Pixel watermark detection completed", "watermark.default.watermarkText": "Watermark", "watermark.error.addImageWatermarkFailed": "Failed to add image watermark", "watermark.error.addTextWatermarkFailed": "Failed to add text watermark", "watermark.error.contentWatermarkDetectionFailed": "Content watermark detection failed", "watermark.error.imageWatermarkDetectionFailed": "Image watermark detection failed", "watermark.filename.watermarkAdded": "_watermark_added", "watermark.filename.watermarkIndexDetected": "_watermark_index_detected", "watermark.filename.watermarkRemoved": "_watermark_removed", "watermark.log.font": "Font", "watermark.log.fontSize": "Font size", "watermark.log.watermarkText": "Watermark text", "watermark.progress.convertingPdfToImage": "Converting PDF to image for pixel analysis", "watermark.progress.detectingContentWatermark": "Detecting content watermark", "watermark.progress.detectingImageWatermark": "Detecting image watermark", "watermark.progress.detectingPathWatermark": "Detecting path watermark", "watermark.progress.processingContentWatermark": "Processing content watermark", "watermark.progress.processingEditableTextWatermark": "Processing editable text watermark", "watermark.progress.processingImageWatermark": "Processing image watermark", "watermark.progress.processingPathWatermark": "Processing path watermark", "watermark.progress.processingPixelWatermark": "Processing pixel watermark"}