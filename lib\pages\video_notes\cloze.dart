import 'dart:convert';
import 'dart:io';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';

class LifecycleEventHandler extends WidgetsBindingObserver {
  final Function() onOrientationChange;

  LifecycleEventHandler({
    required this.onOrientationChange,
  });

  @override
  void didChangeMetrics() {
    onOrientationChange();
  }
}

class VideoImageCloze extends StatefulWidget {
  final Function(List<Rect> annotations)? onAnnotationsChanged;

  const VideoImageCloze({
    super.key,
    this.onAnnotationsChanged,
  });

  @override
  State<VideoImageCloze> createState() => _VideoImageClozeState();
}

class _VideoImageClozeState extends State<VideoImageCloze> {
  List<Rect> annotations = [];
  List<Rect> undoHistory = [];
  List<Rect> redoHistory = [];
  Offset? _startPoint;
  Rect? _currentRect;
  int? _selectedRectIndex; // 当前选中的矩形索引
  bool _isMoving = false; // 是否正在移动矩形
  Offset? _lastPosition; // 用于计算移动距离
  bool _isFilled = true; // 默认为填充状态
  bool _isResizing = false; // 新增：是否正在调整大小

  // 新增：用于存储图片实际显示区域
  Rect? _imageDisplayRect;

  // 添加图片尺寸缓存
  Size? _cachedImageSize;

  // 缓存图片显尺寸的计算结果
  Size? _cachedDisplaySize;
  Size? _lastContainerSize;

  // 添加一个变量来存储相对坐标
  List<List<double>> _relativeAnnotations = [];
  final controller = Get.find<ImageCardController>();
  final settingController = Get.find<SettingController>();
  final videoNoteController = Get.find<VideoNoteController>();
  final _storage = StorageManager();

  // 优化获取图片尺寸的方法
  Size _getImageSize(Uint8List imageBytes) {
    if (_cachedImageSize != null) return _cachedImageSize!;

    try {
      final img.Image? image = img.decodeImage(imageBytes);
      if (image != null) {
        _cachedImageSize =
            Size(image.width.toDouble(), image.height.toDouble());
        return _cachedImageSize!;
      }
    } catch (e) {
      print('Error decoding image: $e');
    }

    return Size.zero;
  }

  // 优化显示尺寸计算
  Size _getImageDisplaySize(Size containerSize, Size imageSize) {
    // 如果容器尺寸没变且已有缓存，直接返回缓存结果
    if (_cachedDisplaySize != null && _lastContainerSize == containerSize) {
      return _cachedDisplaySize!;
    }

    final double aspectRatio = imageSize.width / imageSize.height;
    final double containerAspectRatio =
        containerSize.width / containerSize.height;

    Size result;
    if (containerAspectRatio > aspectRatio) {
      final double height = containerSize.height;
      final double width = height * aspectRatio;
      result = Size(width, height);
    } else {
      final double width = containerSize.width;
      final double height = width / aspectRatio;
      result = Size(width, height);
    }

    // 更新缓存
    _cachedDisplaySize = result;
    _lastContainerSize = containerSize;
    return result;
  }

  // 新增：计算图片显示区域
  Rect _calculateImageDisplayRect(Size containerSize, Size imageSize) {
    final Size displaySize = _getImageDisplaySize(containerSize, imageSize);
    final double xOffset = (containerSize.width - displaySize.width) / 2;
    final double yOffset = (containerSize.height - displaySize.height) / 2;
    return Rect.fromLTWH(
        xOffset, yOffset, displaySize.width, displaySize.height);
  }

  // 新增：检查并调整点击位置到图片区域内
  Offset? _adjustPositionToImage(Offset position) {
    if (_imageDisplayRect == null) return null;
    if (!_imageDisplayRect!.contains(position)) return null;
    return position;
  }

  // 检查点击位置是否在某个矩形内
  int? _getRectIndexAtPoint(Offset point) {
    for (int i = annotations.length - 1; i >= 0; i--) {
      if (annotations[i].contains(point)) {
        return i;
      }
    }
    return null;
  }

  // 删除选中的矩形
  void _deleteSelectedRect() {
    if (_selectedRectIndex != null) {
      setState(() {
        redoHistory.clear();
        undoHistory.add(annotations[_selectedRectIndex!]);
        annotations.removeAt(_selectedRectIndex!);
        _selectedRectIndex = null;
        widget.onAnnotationsChanged?.call(annotations);
      });
    }
  }

  void _undo() {
    if (annotations.isEmpty) return;

    setState(() {
      // 将最后一个矩形移到恢复历史
      redoHistory.add(annotations.last);
      annotations.removeLast();
      widget.onAnnotationsChanged?.call(annotations);
    });
  }

  void _redo() {
    if (redoHistory.isEmpty) return;

    setState(() {
      // 将恢复历史中的最后一个矩形恢复
      annotations.add(redoHistory.last);
      redoHistory.removeLast();
      widget.onAnnotationsChanged?.call(annotations);
    });
  }

  List<List<double>> _getRelativeCoordinates(Size containerSize) {
    if (_imageDisplayRect == null) return [];

    return annotations.map((rect) {
      // 计算相对于图片显示区域的坐标
      final relativeX =
          (rect.left - _imageDisplayRect!.left) / _imageDisplayRect!.width;
      final relativeY =
          (rect.top - _imageDisplayRect!.top) / _imageDisplayRect!.height;
      final relativeWidth = rect.width / _imageDisplayRect!.width;
      final relativeHeight = rect.height / _imageDisplayRect!.height;

      return [
        relativeX.clamp(0.0, 1.0),
        relativeY.clamp(0.0, 1.0),
        relativeWidth.clamp(0.0, 1.0),
        relativeHeight.clamp(0.0, 1.0),
      ].map((e) => double.parse(e.toStringAsFixed(4))).toList();
    }).toList();
  }

  // 新增：检查点是否在锚点上
  bool _isOnAnchor(Offset point) {
    if (_selectedRectIndex == null) return false;
    final rect = annotations[_selectedRectIndex!];
    final anchorRect = _getAnchorRect(rect);
    return anchorRect.contains(point);
  }

  // 新增：获取锚点的Rect
  Rect _getAnchorRect(Rect rect) {
    const anchorSize = 10.0;
    return Rect.fromCenter(
      center: rect.bottomRight,
      width: anchorSize,
      height: anchorSize,
    );
  }

  // 新增：保存相对坐标
  void _saveRelativeCoordinates() {
    if (_imageDisplayRect == null) return;
    _relativeAnnotations = annotations.map((rect) {
      final relativeX =
          (rect.left - _imageDisplayRect!.left) / _imageDisplayRect!.width;
      final relativeY =
          (rect.top - _imageDisplayRect!.top) / _imageDisplayRect!.height;
      final relativeWidth = rect.width / _imageDisplayRect!.width;
      final relativeHeight = rect.height / _imageDisplayRect!.height;

      return [relativeX, relativeY, relativeWidth, relativeHeight];
    }).toList();
  }

  // 新增：从相对坐标更新矩形
  void _updateAnnotationsFromRelative() {
    if (_imageDisplayRect == null) return;
    annotations = _relativeAnnotations.map((coord) {
      return Rect.fromLTWH(
        _imageDisplayRect!.left + coord[0] * _imageDisplayRect!.width,
        _imageDisplayRect!.top + coord[1] * _imageDisplayRect!.height,
        coord[2] * _imageDisplayRect!.width,
        coord[3] * _imageDisplayRect!.height,
      );
    }).toList();
  }

  // 修改添加新矩形的逻辑
  void _addNewRect(Rect rect) {
    setState(() {
      annotations.add(rect);
      // 保存新矩形的相对坐标
      final relativeX =
          (rect.left - _imageDisplayRect!.left) / _imageDisplayRect!.width;
      final relativeY =
          (rect.top - _imageDisplayRect!.top) / _imageDisplayRect!.height;
      final relativeWidth = rect.width / _imageDisplayRect!.width;
      final relativeHeight = rect.height / _imageDisplayRect!.height;

      _relativeAnnotations.add([
        relativeX,
        relativeY,
        relativeWidth,
        relativeHeight,
      ]);

      widget.onAnnotationsChanged?.call(annotations);
    });
  }

  // 将图片数据保存为临时文件并返回路径
  Future<String> _saveImageToTemp(Uint8List imageData) async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File(
      '${tempDir.path}/image_${DateTime.now().millisecondsSinceEpoch}.jpg',
    );
    await tempFile.writeAsBytes(imageData);
    return tempFile.path;
  }

  // 修改 initState
  @override
  void initState() {
    super.initState();

    // 修改屏幕旋转监听
    WidgetsBinding.instance.addObserver(
      LifecycleEventHandler(
        onOrientationChange: () {
          if (mounted) {
            // 保存当前相对坐标
            _saveRelativeCoordinates();
            setState(() {
              // 使用保存的相对坐标重新计算矩形位置
              _updateAnnotationsFromRelative();
            });
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(
      LifecycleEventHandler(onOrientationChange: () {}),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ImageCardController>();
    final settingController = Get.find<SettingController>();
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('图片挖空'),
        centerTitle: true,
        actions: [
          // 删除按钮
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _selectedRectIndex != null ? _deleteSelectedRect : null,
            tooltip: '删除选中区域',
          ),
          IconButton(
            icon: const Icon(Icons.undo),
            onPressed: annotations.isEmpty ? null : _undo,
            tooltip: '撤销',
          ),
          IconButton(
            icon: const Icon(Icons.redo),
            onPressed: redoHistory.isEmpty ? null : _redo,
            tooltip: '恢复',
          ),
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: () async {
              final coordinates = _getRelativeCoordinates(
                MediaQuery.of(context).size,
              );
              try {
                final wrappedCoordinates =
                    coordinates.map((coord) => [coord]).toList();

                const platform = MethodChannel('samples.flutter.dev/battery');
                final parentDeck = _storage.read(StorageBox.videoNotes,
                    VideoNotesStorageKeys.defaultDeck, "Guru导入");

                final clozeMode = _storage.read(StorageBox.videoNotes,
                    VideoNotesStorageKeys.defaultClozeMode, "free_guess");

                final isOneClozePerCard = _storage.read(StorageBox.videoNotes,
                    VideoNotesStorageKeys.oneClozePeCard, false);

                final baseTags = _storage
                    .read(
                        StorageBox.videoNotes, ImageCardStorageKeys.defaultTags)
                    .toString()
                    .split(',')
                    .where((tag) => tag.isNotEmpty)
                    .toList();
                final videoNoteController = Get.find<VideoNoteController>();
                final finalLink =
                    videoNoteController.getLinkText(linkFormat: "html");
                String imgPath =
                    await _saveImageToTemp(controller.byteArray.value!);
                String filename = PathUtils(imgPath).name;
                String mediaRes = "<img src='$filename' />";
                if (settingController.cardMode.value == "ankidroid") {
                  final resp = await AnkiConnectController()
                      .addMediaToAnkiDroid(imgPath);
                  if (resp.status == "success") {
                    mediaRes = resp.data;
                  }
                }
                // 添加笔记
                var fieldsList = [];
                var tagsList = [];
                final id = "image_${DateTime.now().millisecondsSinceEpoch}";

                if ((clozeMode == "mask_one_guess_one" ||
                        clozeMode == "mask_all_guess_one") &&
                    isOneClozePerCard) {
                  var mode = "${clozeMode}_multi";
                  for (var i = 0; i < wrappedCoordinates.length; i++) {
                    fieldsList.add([
                      "${id}_${i + 1}",
                      "",
                      mediaRes,
                      "",
                      jsonEncode(wrappedCoordinates),
                      finalLink,
                      "",
                      mode,
                      "c${i + 1}",
                      "",
                      "",
                    ]);
                    tagsList.add(baseTags);
                  }
                } else {
                  fieldsList.add([
                    id,
                    "",
                    mediaRes,
                    "",
                    jsonEncode(wrappedCoordinates),
                    finalLink,
                    "",
                    clozeMode,
                    "1",
                    "",
                    ""
                  ]);
                  tagsList.add(baseTags);
                }
                List<AnkiNote> notes = [];
                for (var i = 0; i < fieldsList.length; i++) {
                  notes.add(AnkiNote(
                      deckName: parentDeck,
                      modelName: InternalModelName.KEVIN_IMAGE_CLOZE,
                      fields: fieldsList[i],
                      tags: tagsList[i]));
                }
                if (settingController.cardMode.value == "ankidroid") {
                  final resp =
                      await AnkiConnectController().uploadToAnkiDroid(notes);
                  logger.i("resp: $resp");
                  if (resp.status == "success") {
                    showToastNotification(
                        context, "${fieldsList.length}张卡片已添加到Anki", "",
                        type: "success");
                  } else {
                    showToastNotification(context, "卡片添加失败", "", type: "error");
                  }
                  Get.back();
                } else if (settingController.cardMode.value == "ankiconnect") {
                  String outputPath = await PathUtils.getOutputApkgPath();
                  final resp = await AnkiConnectController()
                      .genApkg(notes, [imgPath], outputPath);
                  if (resp.status == "success") {
                    await AnkiConnectController()
                        .importApkg(outputPath, isDelete: true);
                    showToastNotification(context, "卡片已保存到Anki", "",
                        type: "success");
                    Get.back();
                  } else {
                    showToastNotification(context, "卡片生成失败", "", type: "error");
                  }
                } else if (settingController.cardMode.value == "apkg") {
                  final progressController = Get.find<ProgressController>();

                  String outputPath = await PathUtils.getOutputApkgPath();
                  logger.i("outputPath: $outputPath");
                  final resp = await AnkiConnectController()
                      .genApkg(notes, [imgPath], outputPath);
                  logger.i("resp: $resp");
                  if (resp.status == "success") {
                    Get.back();
                    progressController.reset(
                      showOutputHint: true,
                      numberButtons: 2,
                    );
                    progressController.showProgressDialog(context);
                    progressController.outputPath.value = resp.data;
                    progressController.updateProgress(
                        status: "completed", message: 'common.completed'.tr);
                    // showToastNotification(context, "卡片已保存到本地", "",
                    //     type: "success");
                  } else {
                    showToastNotification(context, "卡片保存失败", "", type: "error");
                  }
                }
              } catch (err) {
                showToastNotification(context, "$err", "", type: "error");
              }
            },
            tooltip: '完成',
          ),
        ],
      ),
      body: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final Uint8List imageBytes = controller.byteArray.value!;
            final Size imageSize = _getImageSize(imageBytes);
            if (imageSize == Size.zero) return const SizedBox();

            final Size containerSize =
                Size(constraints.maxWidth, constraints.maxHeight);
            final Rect newImageDisplayRect =
                _calculateImageDisplayRect(containerSize, imageSize);

            if (_imageDisplayRect != newImageDisplayRect) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  _imageDisplayRect = newImageDisplayRect;
                  // 使用保存的相对坐标更新矩形位置
                  if (_relativeAnnotations.isNotEmpty) {
                    annotations = _relativeAnnotations.map((coord) {
                      return Rect.fromLTWH(
                        _imageDisplayRect!.left +
                            coord[0] * _imageDisplayRect!.width,
                        _imageDisplayRect!.top +
                            coord[1] * _imageDisplayRect!.height,
                        coord[2] * _imageDisplayRect!.width,
                        coord[3] * _imageDisplayRect!.height,
                      );
                    }).toList();
                  }
                });
              });
            }
            _imageDisplayRect = newImageDisplayRect;

            return Stack(
              children: [
                // 图片层
                Positioned.fromRect(
                  rect: _imageDisplayRect!,
                  child: Image.memory(
                    controller.byteArray.value!,
                    fit: BoxFit.fill, // 改为fill以完全填充计算好的区域
                  ),
                ),

                // 手势层
                GestureDetector(
                  onPanStart: (details) {
                    final adjustedPosition =
                        _adjustPositionToImage(details.localPosition);
                    if (adjustedPosition == null) return;

                    // 检查是否点击在锚点上
                    if (_selectedRectIndex != null &&
                        _isOnAnchor(adjustedPosition)) {
                      setState(() {
                        _isResizing = true;
                        _lastPosition = adjustedPosition;
                        redoHistory.clear();
                      });
                      return;
                    }

                    final hitIndex = _getRectIndexAtPoint(adjustedPosition);
                    if (hitIndex != null) {
                      setState(() {
                        _isMoving = true;
                        _selectedRectIndex = hitIndex;
                        _lastPosition = adjustedPosition;
                        redoHistory.clear();
                      });
                    } else {
                      setState(() {
                        _startPoint = adjustedPosition;
                        _selectedRectIndex = null;
                        redoHistory.clear();
                      });
                    }
                  },
                  onPanUpdate: (details) {
                    if (_imageDisplayRect == null) return;

                    final adjustedPosition =
                        _adjustPositionToImage(details.localPosition);
                    if (adjustedPosition == null) return;

                    if (_isResizing &&
                        _selectedRectIndex != null &&
                        _lastPosition != null) {
                      setState(() {
                        final oldRect = annotations[_selectedRectIndex!];
                        // 创建新的矩形，保持左上角不变，更新右下角
                        final newRect = Rect.fromPoints(
                          oldRect.topLeft,
                          Offset(
                            adjustedPosition.dx.clamp(_imageDisplayRect!.left,
                                _imageDisplayRect!.right),
                            adjustedPosition.dy.clamp(_imageDisplayRect!.top,
                                _imageDisplayRect!.bottom),
                          ),
                        );
                        annotations[_selectedRectIndex!] = newRect;
                        _lastPosition = adjustedPosition;
                      });
                    } else if (_isMoving &&
                        _selectedRectIndex != null &&
                        _lastPosition != null) {
                      setState(() {
                        final delta = adjustedPosition - _lastPosition!;
                        final oldRect = annotations[_selectedRectIndex!];
                        final newRect = oldRect.translate(delta.dx, delta.dy);

                        // 确保矩形不会超出图片范围
                        if (_imageDisplayRect!.contains(newRect.topLeft) &&
                            _imageDisplayRect!.contains(newRect.bottomRight)) {
                          annotations[_selectedRectIndex!] = newRect;
                          _lastPosition = adjustedPosition;
                        }
                      });
                    } else if (_startPoint != null) {
                      setState(() {
                        _currentRect =
                            Rect.fromPoints(_startPoint!, adjustedPosition);
                      });
                    }
                  },
                  onPanEnd: (details) {
                    if (_isResizing) {
                      setState(() {
                        _isResizing = false;
                        _lastPosition = null;
                        widget.onAnnotationsChanged?.call(annotations);
                      });
                    } else if (_isMoving) {
                      setState(() {
                        _isMoving = false;
                        _lastPosition = null;
                        widget.onAnnotationsChanged?.call(annotations);
                      });
                    } else if (_currentRect != null) {
                      setState(() {
                        if (_imageDisplayRect!.overlaps(_currentRect!)) {
                          final clampedRect = Rect.fromLTRB(
                            _currentRect!.left.clamp(_imageDisplayRect!.left,
                                _imageDisplayRect!.right),
                            _currentRect!.top.clamp(_imageDisplayRect!.top,
                                _imageDisplayRect!.bottom),
                            _currentRect!.right.clamp(_imageDisplayRect!.left,
                                _imageDisplayRect!.right),
                            _currentRect!.bottom.clamp(_imageDisplayRect!.top,
                                _imageDisplayRect!.bottom),
                          );
                          _addNewRect(clampedRect);
                        }
                        _currentRect = null;
                        _startPoint = null;
                        widget.onAnnotationsChanged?.call(annotations);
                      });
                    }
                  },
                  child: CustomPaint(
                    size: Size(constraints.maxWidth, constraints.maxHeight),
                    painter: AnnotationPainter(
                      annotations: annotations,
                      currentRect: _currentRect,
                      selectedIndex: _selectedRectIndex,
                      isFilled: _isFilled,
                      imageRect: _imageDisplayRect!,
                    ),
                  ),
                ),

                // 底部切换按钮
                Positioned(
                  right: MediaQuery.of(context).orientation ==
                          Orientation.landscape
                      ? 16 // 横屏时靠右
                      : 0, // 竖屏时设置右边界
                  left: MediaQuery.of(context).orientation ==
                          Orientation.landscape
                      ? null // 横屏时不设置左边距
                      : 0, // 竖屏时设置左边界
                  bottom: MediaQuery.of(context).orientation ==
                          Orientation.landscape
                      ? _imageDisplayRect!.top +
                          (_imageDisplayRect!.height / 2) // 相对于图片区域垂直居中
                      : 16, // 竖屏时距底部16
                  child: Center(
                    // 使用 Center 实现水平居中
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color.fromRGBO(0, 0, 0, 0.6),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: IconButton(
                        icon: Icon(
                          _isFilled ? Icons.visibility : Icons.visibility_off,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          setState(() {
                            _isFilled = !_isFilled;
                          });
                        },
                        tooltip: _isFilled ? '显示' : '隐藏',
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

// 优化绘制器
class AnnotationPainter extends CustomPainter {
  final List<Rect> annotations;
  final Rect? currentRect;
  final int? selectedIndex;
  final bool isFilled;
  final Rect imageRect; // 新增图片区域参数

  AnnotationPainter({
    required this.annotations,
    this.currentRect,
    this.selectedIndex,
    required this.isFilled,
    required this.imageRect,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 使用saveLayer来优化绘制性能
    canvas.saveLayer(imageRect, Paint());

    // 创建一次Paint对象重复使用
    final paint = Paint()
      ..color = Colors.red.withValues(alpha: isFilled ? 1.0 : 0.3)
      ..style = PaintingStyle.fill;

    final selectedPaint = Paint()
      ..color = Colors.red.withValues(alpha: isFilled ? 1.0 : 0.3)
      ..style = PaintingStyle.fill
      ..strokeWidth = 3.0;

    // 批量绘制矩形
    for (var i = 0; i < annotations.length; i++) {
      canvas.drawRect(
          annotations[i], i == selectedIndex ? selectedPaint : paint);
    }

    // 单独处理选中矩形的锚点
    if (selectedIndex != null) {
      final rect = annotations[selectedIndex!];
      _drawAnchorPoint(canvas, rect.bottomRight);
    }

    if (currentRect != null) {
      canvas.drawRect(currentRect!, paint);
    }

    canvas.restore();
  }

  void _drawAnchorPoint(Canvas canvas, Offset position) {
    final anchorPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = const Color.fromARGB(255, 241, 164, 48)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    const anchorRadius = 5.0;
    canvas.drawCircle(position, anchorRadius, anchorPaint);
    canvas.drawCircle(position, anchorRadius, borderPaint);
  }

  @override
  bool shouldRepaint(covariant AnnotationPainter oldDelegate) {
    return annotations != oldDelegate.annotations ||
        currentRect != oldDelegate.currentRect ||
        selectedIndex != oldDelegate.selectedIndex ||
        isFilled != oldDelegate.isFilled ||
        imageRect != oldDelegate.imageRect;
  }
}
