import 'dart:io';

import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:dio/dio.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class ClozeParams extends GetxController {
  // 已有数据
  final cardModelDict = {
    "Kevin Image Cloze v5": "Kevin Image Cloze v5",
    "Image Occlusion": "anki.pdf_card.image_occlusion_builtin".tr,
  };
  final maskTypeList = [
    {"label": "anki.pdf_card.mask_type_square".tr, "value": "square"},
    {"label": "anki.pdf_card.mask_type_highlight".tr, "value": "highlight"},
    {"label": "anki.pdf_card.mask_type_underline".tr, "value": "underline"},
    {"label": "anki.pdf_card.mask_type_squiggly".tr, "value": "squiggly"},
    {"label": "anki.pdf_card.mask_type_strikeout".tr, "value": "strikeout"},
  ].obs;
  // 表单参数
  final mode = "free_guess".obs;
  final primaryMaskColor = const Color(0xFFFF5656).obs;
  final secondaryMaskColor = const Color(0xFFFFEBA2).obs;
  final isPerClozePerCard = false.obs;
  final isFullPageCloze = false.obs;
}

class QaParams extends GetxController {
  final annotTypeList = [
    // {"label": "anki.pdf_card.mask_type_square".tr, "value": "square"},
    {"label": "anki.pdf_card.mask_type_highlight".tr, "value": "highlight"},
    {"label": "anki.pdf_card.mask_type_underline".tr, "value": "underline"},
    {"label": "anki.pdf_card.annot_type_note".tr, "value": "note"},
    {"label": "anki.pdf_card.annot_type_text".tr, "value": "text"},
  ].obs;
  final maskTypes = <String>[
    // "square",
    "highlight",
    "underline",
    "squiggly",
    "strikeout",
    "note"
  ].obs;
  final annotTypes = <String>["highlight", "underline"].obs;
  final zoteroClozeGrammarList = <String>["{{xx}}"].obs;
  final mode = "single_file".obs;
}

class PDFCardFormController extends GetxController {
  // 已有数据
  final tabController = ShadTabsController(value: 'cloze');
  final clozeModeList = [
    {"label": "anki.pdf_card.cloze_mode_mask_one_guess_one".tr, "value": "mask_one_guess_one"},
    {"label": "anki.pdf_card.cloze_mode_mask_all_guess_one".tr, "value": "mask_all_guess_one"},
    {"label": "anki.pdf_card.cloze_mode_mask_all_guess_all".tr, "value": "mask_all_guess_all"},
    // {"label": "遮半猜一", "value": "mask_half_guess_one"},
    {"label": "anki.pdf_card.cloze_mode_free_guess".tr, "value": "free_guess"},
    {"label": "anki.pdf_card.cloze_mode_scratch_guess".tr, "value": "scratch_guess"},
  ].obs;
  final qaModeList = [
    {"label": "anki.pdf_card.qa_mode_single_file".tr, "value": "single_file"},
    {"label": "anki.pdf_card.qa_mode_dual_file".tr, "value": "dual_file"},
    {"label": "anki.pdf_card.qa_mode_single_page".tr, "value": "single_page"},
    {"label": "anki.pdf_card.qa_mode_dual_page".tr, "value": "dual_page"},
  ].obs;
  final qaModeListforZotero = [
    {"label": "anki.pdf_card.qa_mode_note".tr, "value": "note"},
    {"label": "anki.pdf_card.qa_mode_single_file".tr, "value": "single_file"},
    {"label": "anki.pdf_card.qa_mode_dual_file".tr, "value": "dual_file"},
    {"label": "anki.pdf_card.qa_mode_single_page".tr, "value": "single_page"},
    {"label": "anki.pdf_card.qa_mode_dual_page".tr, "value": "dual_page"},
  ].obs;
  final clozeGrammarList = [
    {"label": "{{xx}}", "value": "{{xx}}"},
    {"label": "[[xx]]", "value": "[[xx]]"},
    {"label": "**xx**", "value": "**xx**"},
  ].obs;
  // 表单参数
  final parentDeck = 'anki.pdf_card.guru_import'.tr.obs;
  final isCreateSubDeck = false.obs;
  final cardModel = "Kevin Image Cloze v5".obs;
  final maskTypes = <String>["square", "highlight", "underline"].obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;
  final nCols = 1.obs;
  final qFile = "".obs;
  final aFile = "".obs;
  final qItemId = "".obs;
  final aItemId = "".obs;
  final pageRange = "".obs;
  final qPageRange = "".obs;
  final aPageRange = "".obs;
  final extraInfo = ''.obs;
  final isMixCloze = false.obs;
  final isAnswerCloze = false.obs;
  final isZotero = false.obs;
  final isOCR = false.obs;
  final isShowSource = true.obs;
  final itemKey = "".obs;
  final clozeParams = Get.put(ClozeParams());
  final qaParams = Get.put(QaParams());
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();

  @override
  void onInit() async {
    super.onInit();
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
  }

  @override
  void onClose() {
    clozeParams.dispose();
    qaParams.dispose();
    super.onClose();
  }

  Future<void> updateFieldList(String modelName) async {
    try {
      await AnkiConnectController().updateFieldList(modelName);
      update();
    } catch (e) {
      logger.e("updateFieldList error: $e");
    }
  }

  Future<RustResponse> exportZoteroItem(int itemId) async {
    final data = {
      "item_id": itemId,
      "show_progress": false,
    };
    final resp = await messageController.request(data, "zotero/export_item");
    logger.i("resp: $resp");
    return resp;
  }

  Future<CustomResponse> getZoteroItemInfo(int itemId) async {
    try {
      final dio = Dio();
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_item?id=$itemId");
      return CustomResponse(
          status: response.data['status'].toString(),
          data: jsonEncode(response.data['data']),
          message: response.data['message'].toString());
    } catch (e) {
      logger.e("getZoteroItemInfo error: $e");
      return CustomResponse(status: "error", data: "", message: e.toString());
    }
  }

  Future<CustomResponse> addPageAnnot(
      String path, String pageRange, String outputPath) async {
    try {
      // 加载 PDF 文档
      final PdfDocument document =
          PdfDocument(inputBytes: File(path).readAsBytesSync());
      final List<int> pages = parsePageRange(pageRange, document.pages.count);

      for (final pageIndex in pages) {
        if (document.pages[pageIndex].annotations.count == 0) {
          continue;
        }
        // 创建矩形注释
        PdfRectangleAnnotation rectangleAnnotation = PdfRectangleAnnotation(
            Rect.fromLTWH(0, 0, document.pages[pageIndex].size.width,
                document.pages[pageIndex].size.height),
            '',
            author: 'Syncfusion',
            color: PdfColor(255, 0, 0, 0),
            setAppearance: true,
            modifiedDate: DateTime.now());
        // 将注释添加到指定页面
        document.pages[pageIndex].annotations.add(rectangleAnnotation);
      }
      // 保存文档
      final List<int> bytes = await document.save();
      document.dispose();
      File(outputPath).writeAsBytesSync(bytes);
      // 返回成功响应
      return CustomResponse(
          status: "success",
          data: base64Encode(bytes),
          message: "Annotation added successfully.");
    } catch (e) {
      logger.e("addPageAnnot error: $e");
      return CustomResponse(status: "error", data: "", message: e.toString());
    }
  }

  // 提交表单
  void submit(context) async {
    if (isZotero.value && (Platform.isAndroid || Platform.isIOS)) {
      showToastNotification(context, "anki.pdf_card.zotero_windows_mac_only".tr, "",
          type: "error");
      return;
    }
    // 获取导出模式
    final exportCardMode = settingController.getExportCardMode();
    final ankiConnectUrl = settingController.ankiConnectUrl.value;

    // 显示进度对话框
    progressController.reset(
      showOutputHint: exportCardMode == "apkg",
      numberButtons: exportCardMode == "apkg" ? 2 : 0,
    );
    progressController.showProgressDialog(context);
    logger.i("submit");
    try {
      if (tabController.selected == "cloze") {
        List<String> finalPathList = [];
        if (isZotero.value) {
          final int? itemId = int.tryParse(qItemId.value);
          if (itemId == null) {
            progressController.updateProgress(status: "error", message: "anki.pdf_card.invalid_item_id".trParams({'itemId': qItemId.value}));
            return;
          }
          progressController.updateProgress(
              status: "running", message: "anki.pdf_card.getting_item_info".tr);
          final infoResp = await getZoteroItemInfo(itemId);
          logger.i("infoResp: $infoResp");
          if (infoResp.status != "success") {
            progressController.updateProgress(
                status: "error", message: infoResp.message);
            return;
          }
          final info = jsonDecode(infoResp.data);
          logger.i("info: $info");
          itemKey.value = info['key'];
          progressController.updateProgress(
              status: "running", message: "anki.pdf_card.exporting_item".tr);
          final resp = await exportZoteroItem(itemId);
          if (resp.status == "success") {
            finalPathList = [resp.data];
          } else {
            progressController.updateProgress(
                status: "error", message: resp.message);
            return;
          }
        } else {
          finalPathList = selectedFilePaths.value;
        }
        logger.i("finalPathList: $finalPathList");
        if (finalPathList.isEmpty) {
          progressController.updateProgress(
              status: "error", message: "anki.pdf_card.please_select_file".tr);
          return;
        }
        for (final filePath in finalPathList) {
          final data = {
            "doc_path": filePath, // 文件路径
            "page_range": pageRange.value, // 页码范围
            "address": ankiConnectUrl, // 默认为空
            "deck_name": parentDeck.value, // 牌组名称
            "model_name": "Kevin Image Cloze v5", // 默认为空
            "mask_types": maskTypes.value, // 遮罩类型列表
            "card_type": clozeParams.mode.value, // 制卡模式
            "one_card_per_cloze":
                clozeParams.isPerClozePerCard.value, // 每个挖空一张卡片
            "q_mask_color": clozeParams.primaryMaskColor.value.hex, // 问题遮罩颜色
            "a_mask_color": clozeParams.secondaryMaskColor.value.hex, // 答案遮罩颜色
            "tags": tags, // 标签列表
            "dpi": 300, // 默认DPI
            "is_full_page_mode": clozeParams.isFullPageCloze.value, // 整页模式
            "is_create_subdeck": isCreateSubDeck.value, // 创建子牌组
            "subdeck_max_level": 6, // 子牌组最大层级，默认为6
            "is_show_source": isShowSource.value, // 显示来源
            "is_mix_card": isMixCloze.value, // 混合制卡
            "num_cols": nCols.value, // 列数
            "extra_info": extraInfo.value, // 额外信息
            "export_mode": exportCardMode, // 导出模式，默认为apkg
            "item_key": itemKey.value, // 条目ID
            "output_path": await PathUtils.getOutputApkgPath(), // 输出路径，默认为空
            "data_temp_dir": await PathUtils.tempDir, // 临时目录，默认为空
          };
          final resp = await messageController.request(data, "pdf_card/cloze");
          logger.d("resp: $resp");
          if (resp.status == "success") {
            if (exportCardMode == "ankiconnect") {
              await AnkiConnectController()
                  .importApkg(resp.data, isDelete: true);
            }
            progressController.outputPath.value = resp.data;
            progressController
                .updateProgress(status: "completed", message: 'common.completed'.tr);
          } else {
            progressController.updateProgress(
                status: "error", message: resp.message);
            return;
          }
        }
      } else if (tabController.selected == "qa") {
        if (qaParams.mode.value == "note") {
          final int? itemId = int.tryParse(qItemId.value);
          if (itemId == null) {
            progressController.updateProgress(status: "error", message: "anki.pdf_card.invalid_item_id".trParams({'itemId': qItemId.value}));
            return;
          }
          final data = {
            "item_id": itemId, // 文件路径
            "page_range": pageRange.value, // 页码范围
            "address": ankiConnectUrl, // 默认为空
            "deck_name": parentDeck.value, // 牌组名称
            "model_name": "Kevin Reader Card v2", // 默认为空
            "annotation_types": qaParams.annotTypes.value, // 注释类型，需根据需求填充
            "tags": tags, // 标签列表
            "is_show_source": isShowSource.value, // 显示来源
            "is_answer_cloze": isAnswerCloze.value, // 是否为填空卡
            "cloze_grammar_list": qaParams.zoteroClozeGrammarList.value,
            "extra_info": extraInfo.value, // 额外信息
            "output_path": await PathUtils.getOutputApkgPath(), // 输出路径，默认为空
            "data_temp_dir": await PathUtils.tempDir, // 临时目录，默认为空
          };
          final resp =
              await messageController.request(data, "zotero/make_card");
          logger.d("resp: $resp");
          if (resp.status == "success") {
            if (exportCardMode == "ankiconnect") {
              await AnkiConnectController()
                  .importApkg(resp.data, isDelete: true);
            }
            progressController.outputPath.value = resp.data;
            progressController
                .updateProgress(status: "completed", message: 'common.completed'.tr);
          } else {
            progressController.updateProgress(
                status: "error", message: resp.message);
            return;
          }
        } else if (qaParams.mode.value == "dual_file") {
          String qFilePath = qFile.value;
          String aFilePath = aFile.value;
          if (isZotero.value) {
            final int? itemId = int.tryParse(qItemId.value);
            if (itemId == null) {
              progressController.updateProgress(status: "error", message: "anki.pdf_card.invalid_question_item_id".trParams({'itemId': qItemId.value}));
              return;
            }
            final resp = await exportZoteroItem(itemId);
            logger.i("resp: $resp");
            if (resp.status == "success") {
              qFilePath = resp.data;
            } else {
              progressController.updateProgress(
                  status: "error", message: resp.message);
              return;
            }
            final infoResp = await getZoteroItemInfo(itemId);
            logger.i("infoResp: $infoResp");
            if (infoResp.status != "success") {
              progressController.updateProgress(
                  status: "error", message: infoResp.message);
              return;
            }
            final info = jsonDecode(infoResp.data);
            logger.i("info: $info");
            itemKey.value = info['key'];

            final int? itemId2 = int.tryParse(aItemId.value);
            if (itemId2 == null) {
              progressController.updateProgress(status: "error", message: "anki.pdf_card.invalid_answer_item_id".trParams({'itemId': aItemId.value}));
              return;
            }
            final resp2 = await exportZoteroItem(itemId2);
            logger.i("resp2: $resp2");
            if (resp2.status == "success") {
              aFilePath = resp2.data;
            } else {
              progressController.updateProgress(
                  status: "error", message: resp2.message);
              return;
            }
          }
          if (qFilePath.isEmpty || aFilePath.isEmpty) {
            progressController.updateProgress(
                status: "error", message: "anki.pdf_card.please_select_file".tr);
            return;
          }
          final data = {
            "card_type": qaParams.mode.value, // 制卡模式
            "doc_path": qFilePath, // 文件路径
            "a_doc_path": aFilePath, // 备用文件路径
            "page_range": pageRange.value, // 页码范围
            "a_page_range": aPageRange.value, // 备用页码范围
            "address": ankiConnectUrl, // 默认为空
            "deck_name": parentDeck.value, // 牌组名称
            "model_name": "Kevin Image QA Card v2", // 默认为空
            "mask_types": maskTypes.value, // 遮罩类型列表
            "tags": tags, // 标签列表
            "dpi": 300, // 默认DPI
            "is_create_subdeck": isCreateSubDeck.value, // 创建子牌组
            "subdeck_max_level": 0, // 子牌组最大层级，默认为0
            "is_mix_card": isMixCloze.value, // 混合制卡
            "is_answer_cloze": isAnswerCloze.value, // 是否为填空卡
            "is_show_source": isShowSource.value, // 显示来源
            "num_cols": nCols.value, // 列数
            "extra_info": extraInfo.value, // 额外信息
            "item_key": itemKey.value.isNotEmpty ? itemKey.value : null, // 项目键
            "output_path": await PathUtils.getOutputApkgPath(), // 输出路径，默认为空
          };
          final resp = await messageController.request(data, "pdf_card/qa");
          logger.d("resp: $resp");
          if (resp.status == "success") {
            if (exportCardMode == "ankiconnect") {
              await AnkiConnectController()
                  .importApkg(resp.data, isDelete: true);
            }
            progressController.outputPath.value = resp.data;
            progressController
                .updateProgress(status: "completed", message: 'common.completed'.tr);
          } else {
            progressController.updateProgress(
                status: "error", message: resp.message);
            return;
          }
        } else {
          List<String> finalPathList = [];
          if (isZotero.value) {
            final int? itemId = int.tryParse(qItemId.value);
            if (itemId == null) {
              progressController.updateProgress(status: "error", message: "anki.pdf_card.invalid_item_id".trParams({'itemId': qItemId.value}));
              return;
            }
            progressController.updateProgress(
                status: "running", message: "anki.pdf_card.getting_item_info".tr);
            final infoResp = await getZoteroItemInfo(itemId);
            logger.i("infoResp: $infoResp");
            if (infoResp.status != "success") {
              progressController.updateProgress(
                  status: "error", message: infoResp.message);
              return;
            }
            final info = jsonDecode(infoResp.data);
            logger.i("info: $info");
            itemKey.value = info['key'];
            progressController.updateProgress(
                status: "running", message: "anki.pdf_card.exporting_item".tr);
            final resp = await exportZoteroItem(itemId);
            logger.i("resp: $resp");
            if (resp.status == "success") {
              finalPathList = [resp.data];
            } else {
              progressController.updateProgress(
                  status: "error", message: resp.message);
              return;
            }
          } else {
            finalPathList = selectedFilePaths.value;
          }
          if (finalPathList.isEmpty) {
            progressController.updateProgress(
                status: "error", message: "anki.pdf_card.please_select_file".tr);
            return;
          }
          for (final filePath in finalPathList) {
            final data = {
              "card_type": qaParams.mode.value, // 制卡模式
              "doc_path": filePath, // 文件路径
              "a_doc_path": aFile.value, // 备用文件路径
              "page_range": pageRange.value, // 页码范围
              "a_page_range": aPageRange.value, // 备用页码范围
              "address": ankiConnectUrl, // 默认为空
              "deck_name": parentDeck.value, // 牌组名称
              "model_name": "Kevin Image QA Card v2", // 默认为空
              "mask_types": maskTypes.value, // 遮罩类型列表
              "tags": tags, // 标签列表
              "dpi": 300, // 默认DPI
              "is_create_subdeck": isCreateSubDeck.value, // 创建子牌组
              "subdeck_max_level": 0, // 子牌组最大层级，默认为0
              "is_mix_card": isMixCloze.value, // 混合制卡
              "is_answer_cloze": isAnswerCloze.value, // 是否为填空卡
              "is_show_source": isShowSource.value, // 显示来源
              "num_cols": nCols.value, // 列数
              "extra_info": extraInfo.value, // 额外信息
              "item_key":
                  itemKey.value.isNotEmpty ? itemKey.value : null, // 项目键
              "output_path": await PathUtils.getOutputApkgPath(), // 输出路径，默认为空
            };
            final resp = await messageController.request(data, "pdf_card/qa");
            logger.d("resp: $resp");
            if (resp.status == "success") {
              if (exportCardMode == "ankiconnect") {
                await AnkiConnectController()
                    .importApkg(resp.data, isDelete: true);
              }
              progressController.outputPath.value = resp.data;
              progressController
                  .updateProgress(status: "completed", message: 'common.completed'.tr);
            } else {
              progressController.updateProgress(
                  status: "error", message: resp.message);
              return;
            }
          }
        }
      }
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
          status: "error", message: e.toString());
    }
  }
}
