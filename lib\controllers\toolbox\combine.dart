import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFCombinePageController extends GetxController {
  // 基础数据
  late final List<Map<String, String>> orientationList;
  final List<Map<String, String>> layoutOrderList = [
      {"value": "row_lr", "label": 'toolbox.combine.row_lr'.tr},
      {"value": "row_rl", "label": 'toolbox.combine.row_rl'.tr},
      {"value": "col_lr", "label": 'toolbox.combine.col_lr'.tr},
      {"value": "col_rl", "label": 'toolbox.combine.col_rl'.tr},
  ];
  // 表单参数
  final numCols = 2.obs;
  final numRows = 1.obs;
  final pageSize = "A4".obs;
  final orientation = "portrait".obs;
  final layoutOrder = "row_lr".obs;
  final unit = "pt".obs;
  final top = 0.0.obs;
  final bottom = 0.0.obs;
  final left = 0.0.obs;
  final right = 0.0.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'annotate');

  @override
  void onInit() {
    super.onInit();
    // 初始化时根据当前语言设置orientationList
    orientationList = [
      {"value": "portrait", "label": 'toolbox.combine.portrait'.tr},
      {"value": "landscape", "label": 'toolbox.combine.landscape'.tr},
    ];
  }

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.background.processingFile'.tr}: ${PathUtils(filePath).name}",
        );
        final outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'home.tools.pdfCombine'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );
        final data = {
          'num_cols': numCols.value,
          'num_rows': numRows.value,
          'layout_order': layoutOrder.value,
          'input_path': filePath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };
        final resp = await messageController.request(data, 'pdf/combine');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.combine.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: 'common.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
