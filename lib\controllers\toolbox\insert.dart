import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFInsertPageController extends GetxController {
  // 基本数据
  final insertModeList = [
    {'value': 'blank', 'label': 'toolbox.insert.mode.blank'.tr},
    {'value': 'file', 'label': 'toolbox.insert.mode.file'.tr},
  ];
  final orientationList = [
    {'value': 'portrait', 'label': 'toolbox.insert.orientationOptions.portrait'.tr},
    {'value': 'landscape', 'label': 'toolbox.insert.orientationOptions.landscape'.tr},
  ];
  final positionList = [
    {
      'value': 'before_first',
      'label': 'toolbox.insert.position.before_first'.tr
    },
    {'value': 'after_first', 'label': 'toolbox.insert.position.after_first'.tr},
    {'value': 'before_last', 'label': 'toolbox.insert.position.before_last'.tr},
    {'value': 'after_last', 'label': 'toolbox.insert.position.after_last'.tr},
    {'value': 'before_page', 'label': 'toolbox.insert.position.before_page'.tr},
    {'value': 'after_page', 'label': 'toolbox.insert.position.after_page'.tr},
    {'value': 'before_odd', 'label': 'toolbox.insert.position.before_odd'.tr},
    {'value': 'before_even', 'label': 'toolbox.insert.position.before_even'.tr},
    {'value': 'before_all', 'label': 'toolbox.insert.position.before_all'.tr},
    {'value': 'after_all', 'label': 'toolbox.insert.position.after_all'.tr},
  ];

  // 表单参数
  final insertMode = "blank".obs;
  final insertFile = ''.obs;
  final insertPosition = 'before_first'.obs;
  final insertPage = 1.obs;
  final insertCount = 1.obs;
  final paperSize = 'A4'.obs;
  final orientation = 'portrait'.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  final outputDirError = "".obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      for (String filePath in selectedFilePaths) {
        final pathUtils = PathUtils(filePath);
        String outputPath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.insert.fileNameAppend'.tr}",
          outputDir: outputDir.value,
        );

        final data = {
          'insert_mode': insertMode.value,
          'input_path': filePath,
          'output_path': outputPath,
          'insert_file': insertFile.value,
          'insert_position': insertPosition.value,
          'insert_page': insertPage.value,
          'insert_count': insertCount.value,
          'paper_size': paperSize.value,
          'orientation': orientation.value,
          'show_progress': true,
        };

        final resp = await messageController.request(data, 'pdf_insert');
        if (resp.status == 'success') {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr,
          );
        } else {
          throw Exception(resp.message);
        }
      }

      progressController.updateProgress(status: "completed", message: 'common.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.process.failed'.tr}: $e",
      );
    }
  }
}
